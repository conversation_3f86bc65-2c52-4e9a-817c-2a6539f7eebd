{"name": "elder-wand-umbrella", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:elder-wand": "ng serve elderWand -o --port 4200", "start:experience-studio": "ng serve experienceStudio -o --port 4201", "start:product-studio": "ng serve product-studio -o --port 4202", "start:console": "ng serve console -o --port 4203", "build": "ng build", "test": "ng test", "build:console": "ng build console --configuration production", "build:elder-wand": "ng build elderWand --configuration production", "build:experience-studio": "ng build experienceStudio --configuration production", "build:product-studio": "ng build product-studio --configuration production", "build:all": "npm run build:console && npm run build:elder-wand && npm run build:experience-studio && npm run build:product-studio", "docker:start": "./scripts/start-docker.sh start", "docker:stop": "./scripts/start-docker.sh stop", "docker:status": "./scripts/start-docker.sh status", "docker:rebuild": "./scripts/start-docker.sh rebuild"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/cdk": "19.2.1", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/material": "19.2.1", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/platform-server": "^19.1.0", "@angular/router": "^19.1.0", "@angular/ssr": "^19.2.0", "@ava/play-comp-library": "file:ava-play-comp-library-1.0.80.tgz", "@awe/play-comp-library": "^1.0.30", "@azure/msal-angular": "^4.0.11", "@azure/msal-browser": "^4.11.1", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@types/jszip": "^3.4.0", "chart.js": "^4.4.8", "event-source-polyfill": "^1.0.31", "highcharts": "^11.4.8", "highcharts-angular": "^4.0.1", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "lucide-angular": "^0.525.0", "marked": "^15.0.11", "monaco-editor": "^0.52.2", "ngx-markdown": "^19.1.1", "ngx-slick-carousel": "^19.0.0", "rxjs": "~7.8.0", "slick-carousel": "^1.8.1", "ts-md5": "^1.3.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.1", "@angular-devkit/build-angular": "^19.2.0", "@angular/cli": "^19.1.8", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "compression-webpack-plugin": "^11.1.0", "css-loader": "^7.1.2", "event-source-polyfill": "^1.0.31", "monaco-editor-webpack-plugin": "^7.1.0", "ngx-build-plus": "^19.0.0", "prettier": "^3.5.3", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "typescript": "~5.7.2", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}}