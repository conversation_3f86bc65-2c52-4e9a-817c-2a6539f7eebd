.create-models-container {
  width: 100%;
  height: 100vh;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}
form {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}
.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0;
  padding: 0;
  flex: 1 1 0;
  min-height: 0;
  height: 100%;
  border: 1px solid #e1e4e8;
  background: #ffffff;
  @media (max-width: 1400px) {
    gap: 16px;
    padding: 16px;
  }
  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
    flex-direction: column;
  }
}
.left-column, .right-column {
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  background: #ffffff;
  border-right: 1px solid #e1e4e8;
}
.left-column {
  width: 340px;
  min-width: 60px;
  max-width: 340px;
  transition: width 0.3s cubic-bezier(0.4,0,0.2,1);
  background: #f8f9fa;
}
.right-column {
  flex: 1 1 0;
  min-width: 0;
  min-height: 0;
  border-right: none;
  background: #fff;
}
.right-column-content {
  flex: 1 1 0;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.right-column-content::-webkit-scrollbar {
  display: none;
}
.solid-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none !important;
}
.card-content {
  // border: 2px solid black;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
  }
}
// .left-column {
//   border: 2px solid red;
// }
.right-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.section-title, .selection-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);
  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}
.selection-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}
.model-selection-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-bottom: 24px;
  @media (max-width: 992px) {
    flex-direction: column;
    gap: 16px;
  }
}
.engine-selection,
.model-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.dropdown-container {
  margin-top: 8px;
}
.dropdown-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--form-input-border);
  border-radius: 8px;
  background-color: var(--form-input-bg);
  font-size: 14px;
  color: var(--form-input-color);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
  }
}
.parameters-container {
  margin-top: 16px;
}
.parameter-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 16px;
}
.param-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
.param-field {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}
.param-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--form-label-color);
}
.param-input {
  padding: 10px 12px;
  border: 1px solid var(--form-input-border);
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--form-input-bg);
  color: var(--form-input-color);
  &:focus {
    outline: none;
    border-color: var(--form-input-focus-border);
  }
}
.dropdown, .dropdown-selected, .dropdown-icon, .dropdown-arrow {
  display: none;
}
.dropdown-medium {
  width: 250px;
}
.version-top {
 margin-top: 8px;
}
.ava-list-disabled .ava-list-item {
  pointer-events: none; 
}
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}
.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}
.radio-label {
  font-size: 14px;
  margin-left: 4px;
  color: var(--form-label-color);
}
.right-column-buttons {
  justify-content: flex-end;
  gap: 16px;
  padding: 33px 13px 4px;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
  }
}
.exit-button, .save-button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  @media (max-width: 576px) {
    padding: 8px 16px;
    font-size: 13px;
  }
}
.exit-button {
  background-color: transparent;
  border: 1px solid var(--button-secondary-border);
  color: var(--button-secondary-text);
  &:hover {
    background-color: var(--button-secondary-hover-bg);
  }
}
.save-button {
  background: var(--button-gradient);
  border: none;
  color: var(--button-primary-text);
  &:hover {
    opacity: var(--button-hover-opacity);
  }
}
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140px;
  color: var(--text-secondary);
  font-size: 14px;
  font-style: italic;
  text-align: center;
  background-color: var(--agent-tools-empty-bg);
  border-radius: 6px;
  border: 1px dashed var(--agent-tools-empty-border);
}
/* CSS to make heading and Save button inline */
.header-with-save {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.header-with-save h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}
/* Remove the old right-column-buttons styles since button is now inline */
.right-column-buttons {
  display: none; /* Hide the old button container */
}
/* Responsive adjustments */
@media (max-width: 576px) {
  .header-with-save {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .header-with-save h3 {
    font-size: 14px;
  }
}


.lists-container {
  display: flex;
  gap: 16px;
}
.engine-selection, .model-type-selection, .model-selection {
  flex: 1;
}
.message-wrapper {
  display: flex;
  justify-content: center;  /* Center horizontally */
  align-items: center;      /* Center vertically */
  // height: 100%;             /* Or set a specific height */
}

// .create-models-container {
//   width: 100%;
//   height: 100%;
//   display: flex;
//   flex-direction: column;
//   overflow: hidden;
//   background-color: transparent;
// }
// form {
//   display: flex;
//   flex-direction: column;
//   height: calc(100vh - 300px);
//   overflow: hidden;
// }
// .form-layout {
//   display: flex;
//   flex-direction: row;
//   gap: 0;
//   padding: 0;
//   flex: 1;
//   overflow: hidden;
//   height: 100%;
//   border: 1px solid #e1e4e8;
//   background: #ffffff;
//   @media (max-width: 1400px) {
//     gap: 16px;
//     padding: 16px;
//   }
//   @media (max-width: 1200px) {
//     flex-wrap: wrap;
//   }
//   @media (max-width: 576px) {
//     gap: 12px;
//     padding: 12px;
//     flex-direction: column;
//   }
// }
// .left-column, .right-column {
//   display: flex;
//   flex-direction: column;
//   gap: 20px;
//   height: 100%;
//   padding: 16px;
//   background: #ffffff;
//   border-right: 1px solid #e1e4e8;
//   overflow-y: auto;
//   &:last-child {
//     border-right: none;
//   }
//   @media (max-width: 1400px) {
//     gap: 16px;
//     padding: 16px;
//   }
//   @media (max-width: 1200px) {
//     height: auto;
//     padding: 16px;
//     border-right: none;
//     border-bottom: 1px solid #e1e4e8;
//     overflow-y: visible;
//     &:last-child {
//       border-bottom: none;
//     }
//   }
//   @media (max-width: 576px) {
//     gap: 12px;
//     width: 100% !important;
//     padding: 12px;
//   }
// }
// .left-column {
//   width: 30%;
//   flex-shrink: 0;
//   transition: width 0.3s ease;
//   @media (max-width: 1400px) {
//     width: 40%;
//   }
//   @media (max-width: 1200px) {
//     width: 100%;
//   }
// }
// .right-column {
//   width: 70%;
//   flex-shrink: 0;
//   display: flex;
//   flex-direction: column;
//   min-width: 300px;
//   @media (max-width: 1200px) {
//     width: 100%;
//     min-width: unset;
//   }
// }
// .right-column-content {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
//   height: 100%;
// }
// /* New header section styling */
// .header-section {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   margin-bottom: 20px;
//   padding: 0 16px;
  
//   h3 {
//     margin: 0;
//     font-size: 18px;
//     font-weight: 600;
//     color: var(--text-color);
    
//     @media (max-width: 576px) {
//       font-size: 16px;
//     }
//   }
  
//   @media (max-width: 768px) {
//     flex-direction: column;
//     gap: 12px;
//     align-items: flex-start;
//   }
  
//   @media (max-width: 576px) {
//     padding: 0 12px;
//   }
// }
// .solid-card {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
//   border: none !important;
// }
// .card-content {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
//   gap: 16px;
//   padding: 16px;
//   @media (max-width: 576px) {
//     gap: 12px;
//     padding: 12px;
//   }
// }
// .right-column .card-content {
//   flex: 1;
//   height: 100%;
//   display: flex;
//   flex-direction: column;
// }
// .section-title, .selection-title {
//   font-size: 16px;
//   font-weight: 600;
//   margin: 0 0 8px;
//   color: var(--text-color);
//   @media (max-width: 576px) {
//     font-size: 14px;
//     margin-bottom: 6px;
//   }
// }
// .selection-description {
//   font-size: 14px;
//   color: var(--text-secondary);
//   margin-bottom: 8px;
// }
// .model-selection-container {
//   display: flex;
//   flex-direction: row;
//   gap: 24px;
//   margin-bottom: 24px;
//   @media (max-width: 992px) {
//     flex-direction: column;
//     gap: 16px;
//   }
// }
// .engine-selection,
// .model-selection {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
// }
// .dropdown-container {
//   margin-top: 8px;
// }
// .dropdown-select {
//   width: 100%;
//   padding: 12px 16px;
//   border: 1px solid var(--form-input-border);
//   border-radius: 8px;
//   background-color: var(--form-input-bg);
//   font-size: 14px;
//   color: var(--form-input-color);
//   appearance: none;
//   background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
//   background-repeat: no-repeat;
//   background-position: right 16px center;
//   background-size: 16px;
//   &:focus {
//     outline: none;
//     border-color: var(--form-input-focus-border);
//   }
// }
// .parameters-container {
//   margin-top: 16px;
// }
// .parameter-form {
//   display: flex;
//   flex-direction: column;
//   gap: 24px;
//   margin-top: 16px;
// }
// .param-row {
//   display: grid;
//   grid-template-columns: repeat(2, 1fr);
//   gap: 10px;
// }
// .param-field {
//   display: flex;
//   flex-direction: column;
//   gap: 10px;
//   width: 100%;
// }
// .param-label {
//   font-size: 14px;
//   font-weight: 500;
//   color: var(--form-label-color);
// }
// .param-input {
//   padding: 10px 12px;
//   border: 1px solid var(--form-input-border);
//   border-radius: 6px;
//   font-size: 14px;
//   background-color: var(--form-input-bg);
//   color: var(--form-input-color);
//   &:focus {
//     outline: none;
//     border-color: var(--form-input-focus-border);
//   }
// }
// .dropdown, .dropdown-selected, .dropdown-icon, .dropdown-arrow {
//   display: none;
// }
// .radio-group {
//   display: flex;
//   gap: 20px;
//   margin-top: 10px;
// }
// .radio-option {
//   display: flex;
//   align-items: center;
//   gap: 8px;
//   cursor: pointer;
// }
// .radio-label {
//   font-size: 14px;
//   margin-left: 4px;
//   color: var(--form-label-color);
// }
// /* Remove the old right-column-buttons styling since we moved the button to header */
// .right-column-buttons {
//   display: none;
// }
// .exit-button, .save-button {
//   padding: 10px 24px;
//   border-radius: 6px;
//   font-size: 14px;
//   font-weight: 500;
//   cursor: pointer;
//   transition: all 0.2s ease;
//   @media (max-width: 576px) {
//     padding: 8px 16px;
//     font-size: 13px;
//   }
// }
// .exit-button {
//   background-color: transparent;
//   border: 1px solid var(--button-secondary-border);
//   color: var(--button-secondary-text);
//   &:hover {
//     background-color: var(--button-secondary-hover-bg);
//   }
// }
// .save-button {
//   background: var(--button-gradient);
//   border: none;
//   color: var(--button-primary-text);
//   &:hover {
//     opacity: var(--button-hover-opacity);
//   }
// }
// .empty-state {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   height: 140px;
//   color: var(--text-secondary);
//   font-size: 14px;
//   font-style: italic;
//   text-align: center;
//   background-color: var(--agent-tools-empty-bg);
//   border-radius: 6px;
//   border: 1px dashed var(--agent-tools-empty-border);
// }
.page-title {
  font-weight: 700;
  font-size: 24px;
  color: #23272E;
  margin-bottom: 0;
  padding: 0 0 12px 0;
}

.left-header {
  display: flex;
  align-items: center;
  justify-content:space-between;
  height: 48px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  z-index: 2;
  font-weight: 600;
  font-size: 16px;
  color: #23272E;
}

.header-with-save {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // background: #fff;
  // border-bottom: 1px solid #e1e4e8;
  // height: 48px;
  // padding: 0 16px;
}
.header-with-save h4 {
  font-size: 16px;
  font-weight: 600;
  color: #23272E;
  margin: 0;
}

// Blue underline for active tab/section (if needed)
.left-title.active, .header-with-save .active {
  color: #215AD6;
  border-bottom: 2px solid #215AD6;
  padding-bottom: 6px;
}

.collapse-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  cursor: pointer;
  z-index: 2;
  font-size: 18px;
  border: 1px solid #e1e4e8;
}

.left-title {
  font-weight: 600;
  font-size: 16px;
}

.left-column.collapsed {
  width: 48px;
  min-width: 48px;
  max-width: 48px;
}
.left-column.collapsed .card-content {
  display: none;
}

.configuration-message{
margin-top: 10%;
}
.button-container {
  display: flex;
  gap: 10px;
}