import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../../shared/services/pagination.service';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  PopupComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { Guardrail } from '../../../shared/models/card.model';
import { GuardrailsService } from '../../../shared/services/guardrails.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';
import guardrailsLabels from './constants/guardrails-base.json';

@Component({
  selector: 'app-guardrails',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    IconComponent,
    DropdownComponent,
    PopupComponent,
    LucideAngularModule,
  ],
  providers: [DatePipe],
  templateUrl: './guardrails.component.html',
  styleUrl: './guardrails.component.scss',
})
export class GuardrailsComponent implements OnInit {
  // Labels from constants file
  grLabels = guardrailsLabels.labels;

  searchForm!: FormGroup;
  search: any;
  onSearchClick() {
    throw new Error('Method not implemented.');
  }
  allGuardrails: any = [];
  filteredGuardrails: any[] = [];
  displayedGuardrails: any[] = [];
  isLoading: boolean = false;

  // Delete popup properties
  showDeletePopup: boolean = false;
  guardrailToDelete: any = null;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalPages: number = 1;
  guardrailsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'square-pen', iconName: 'square-pen', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];
  showInfoPopup: boolean = false;
  infoMessage: string = '';
  message: string ="";
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private datePipe: DatePipe,
    private guardrailsService: GuardrailsService,
    private fb: FormBuilder,
    private cdr:ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.searchForm = this.fb.group({
      search: [''],
    });
    this.filteredGuardrails = [...this.allGuardrails];
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
    this.filteredGuardrails = this.allGuardrails.map((item: any) => {
      const formattedDate =
        this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';
      return {
        ...item,
        createdDate: formattedDate,
      };
    });

    // initialize the search listener ONCE
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterGuardrails(searchText);
      });
    
    this.loadGuardrails();
  }

  loadGuardrails(): void {
    this.isLoading= true;
    this.guardrailsService
      .fetchAllGuardrails()
      .subscribe((data: Guardrail[]) => {
        this.allGuardrails = data.map((item: Guardrail) => ({
          id: String(item.id),
          title: item.name,
          description: item.description || 'No description',
          tags: [
            { label: item.configKey, type: 'primary' },
            {
              label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,
              type: 'secondary',
            },
          ],
          actions: [
            {
              action: 'execute',
              icon: 'play_circle',
              tooltip: 'Run guardrail',
            },
            {
              action: 'clone',
              icon: 'content_copy',
              tooltip: 'Clone guardrail',
            },
            { action: 'delete', icon: 'delete', tooltip: 'Delete guardrail' },
          ],
          createdDate: new Date().toLocaleDateString(), // Optional: Use actual created date if available
        }));

        // after data is loaded, filter based on current search text
        const currentSearch =
          this.searchForm.get('search')?.value?.toLowerCase() || '';
        this.filterGuardrails(currentSearch);

        this.filteredGuardrails = [...this.allGuardrails];
        this.updateDisplayedGuardrails();
        this.isLoading= false;
      });
  }

  filterGuardrails(searchText: string): void {
    this.filteredGuardrails = this.allGuardrails.filter((gr: any) => {
      const inTitle = gr.title?.toLowerCase().includes(searchText);
      const inDescription = gr.description?.toLowerCase().includes(searchText);
      const inTags =
        Array.isArray(gr.tags) &&
        gr.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );

      return inTitle || inDescription || inTags;
    });

    this.updateDisplayedGuardrails();
  }

  updateDisplayedGuardrails(): void {
    const result = this.paginationService.getPaginatedItems(
      this.filteredGuardrails,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedGuardrails = result.displayedItems;
    this.totalPages = result.totalPages;
  }

  onCreateGuardrail(): void {
    this.router.navigate(['/libraries/guardrails/create']);
  }

  // onCardClicked(guardrailId: string): void {
  //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {
  //     queryParams: { returnPage: this.currentPage },
  //   });
  // }

  getHeaderIcons(guardrail: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'swords', title: guardrail.toolType || 'Guardrails' },
      { iconName: 'users', title: `${guardrail.userCount || 10}` },
    ];
  }

  getFooterIcons(guardrail: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: guardrail.owner || 'AAVA' },
      { iconName: 'calendar-days', title: guardrail.createdDate },
    ];
  }

  onIconClicked(icon: any, guardrailId: string): void {
    switch (icon.name) {
      case 'square-pen':
        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);
        break;
      case 'trash':
        this.confirmDeleteGuardrail(guardrailId);
        break;
      case 'play':
        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);
        break;
      case 'copy':
        this.duplicateGuardrail(guardrailId);
        break;
      default:
        break;
    }
  }

  confirmDeleteGuardrail(guardrailId: string): void {
    this.guardrailToDelete = this.allGuardrails.find(
      (item: any) => item.id === guardrailId,
    );
    this.message =`Are you sure you want to delete ${this.guardrailToDelete.title} ?`
    this.showDeletePopup = true;
  }

  onConfirmDelete(): void {
    if (this.guardrailToDelete) {
    const successMessage = `Guardrail "${this.guardrailToDelete.title}" has been successfully deleted.`;
      this.guardrailsService
        .deleteGuardrail(Number(this.guardrailToDelete.id))
        .subscribe({
          next: () => {
            console.log('Guardrail deleted');
            this.closeDeletePopup();
         setTimeout(() => {
          this.showInfoPopup = true;
            this.infoMessage = successMessage;
            this.cdr.detectChanges(); 
          }, 0);
          this.loadGuardrails(); // Refresh list
        },
          error: (err) => {
            console.error('Failed to delete guardrail:', err);
            this.closeDeletePopup();
          },
        });
    }
  }
  handleInfoPopupClose(): void {
  this.showInfoPopup = false;
}

  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.guardrailToDelete = null;
  }

  duplicateGuardrail(guardrailId: string): void {
    // Implement duplicate logic
  }

  getTagsLine(guardrail: any): string {
    if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';
    return guardrail.tags
      .map((tag: any) => tag.label?.trim())
      .filter((label: string | undefined) => !!label)
      .join(' | ');
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updateDisplayedGuardrails();
  }

  get showCreateCard(): boolean {
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }
}
