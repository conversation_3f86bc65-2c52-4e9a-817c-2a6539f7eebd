import {
  Component,
  OnInit,
  OnDestroy,
  EventEmitter,
  Output,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';
import { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';
import { ToolsService } from '../../../../shared/services/tools.service';
import { Subscription } from 'rxjs';
import { PromptEnhanceService } from '../../../../shared/services/prompt-enhance.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import {
  CodeEditorComponent,
  CodeLanguage,
  CodeEditorTheme,
  EditorActionButton,
} from '../../../../shared/components/code-editor/code-editor.component';
import toolsText from '../constants/tools.json';
import { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';
import {
  AvaTextareaComponent,
  AvaTextboxComponent,
  ButtonComponent,
  DropdownOption,
  PopupComponent,
} from '@ava/play-comp-library';
import { MainLayoutComponent } from 'projects/console/src/app/shared/components/main-layout/main-layout.component';

interface ExtractedParameter {
  name: string;
  type: string;
}

interface Tool {
  id: number;
  name: string;
}

interface ParameterCollectionState {
  parameters: ExtractedParameter[];
  currentParameterIndex: number;
  collectedInputs: { [key: string]: any };
  isCollecting: boolean;
}

@Component({
  selector: 'app-create-tools',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    AvaTextareaComponent,
    ButtonComponent,
    PlaygroundComponent,
    CodeEditorComponent,
    MainLayoutComponent,
    PopupComponent
  ],
  templateUrl: './create-tools.component.html',
  styleUrls: ['./create-tools.component.scss'],
})
export class CreateToolsComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;

  toolId: number | null = null;
  isEditMode: boolean = false;
  isCloneMode: boolean = false;
  isExecuteMode: boolean = true;
  isFieldsDisabled: boolean = false;
  showChatInterface: boolean = true;
  selectedTool: string | null = null;
  toolForm: FormGroup;
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  parameterState: ParameterCollectionState = {
    parameters: [],
    currentParameterIndex: 0,
    collectedInputs: {},
    isCollecting: false,
  };

  private executionSubscription: Subscription = new Subscription();
  private waitingForRestartConfirmation: boolean = false;
  public placeholder: any = toolsText.TOOL_PLACEHOLDER.toolClassDef;
  public labels: any = toolsText.labels;
  @Output() promptChange = new EventEmitter<string>();
  public validationOutput: string = '';
  public showValidationOutput: boolean = false;
  public validationOutputEditorConfig = {
    title: 'Tool Compiler',
    language: 'json' as CodeLanguage,
    theme: 'light' as CodeEditorTheme,
    readOnly: true,
    height: '250px',
    placeholder: 'Test the tool before saving or updating for security validations',
  };

  public editorActions: EditorActionButton[] = [
    { label: 'Select All', style: 'secondary', customClass: '', icon: '' },
    { label: 'Reset', style: 'secondary', customClass: '', icon: '' },
  ];

  private originalToolData: any = null;
  private codeEditorValueToSet: string | null = null;

  // Popup properties
  showSuccessPopup = false;
  showErrorPopup = false;
  popupTitle = '';
  popupMessage = '';
  private shouldCallExtractParams = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private toolsService: ToolsService,
    private promptGenerateService: PromptEnhanceService,
    private tokenStorage: TokenStorageService,
  ) {
    this.toolForm = this.fb.group({
      name: ['', [Validators.required, this.noWhitespaceValidator]],
      description: ['', [Validators.required, this.noWhitespaceValidator]],
      toolClassName: ['', [Validators.required, this.noWhitespaceValidator, Validators.pattern(/^[A-Z][a-zA-Z0-9]*$/)]],
      classDefinition: ['', [Validators.required, this.noWhitespaceValidator],]
    });
  }

  private getUserSignature(): string {
    const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';
    return userSignature;
  }

  private formatLocalDateTime(date: Date = new Date()): string {
    // Format date as LocalDateTime for Java backend (without timezone)
    // Try to match the format from the example: 2025-06-10T05:09:39.136505
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // Generate 6-digit microseconds (similar to the example format)
    const milliseconds = date.getMilliseconds();
    const microseconds = String(milliseconds * 1000 + Math.floor(Math.random() * 1000)).padStart(6, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}`;
  }

  ngOnInit(): void {
    const toolIdParam = this.route.snapshot.paramMap.get('id');
    this.toolId = toolIdParam ? parseInt(toolIdParam, 10) : null;
    const urlPath = this.router.url;
    this.isEditMode = urlPath.includes('/edit/');
    this.isCloneMode = urlPath.includes('/clone/');
    this.isExecuteMode = urlPath.includes('/execute/');
    this.isFieldsDisabled = this.isExecuteMode;

    console.log('ngOnInit - toolId:', this.toolId, 'isEditMode:', this.isEditMode, 'isCloneMode:', this.isCloneMode, 'isExecuteMode:', this.isExecuteMode);

    if ((this.isEditMode || this.isCloneMode || this.isExecuteMode) && this.toolId) {
      this.loadToolData(this.toolId);
      if (this.isExecuteMode) {
        this.initializeChatMessages();
      }
    }

    // Add listener for toolClassName changes to update classDefinition validation
    this.toolForm.controls['toolClassName'].valueChanges.subscribe(() => {
      this.toolForm.controls['classDefinition'].updateValueAndValidity();
    });

    // Window resize will be handled by HostListener
  }

  ngAfterViewInit(): void {
    // If value was loaded before editor was ready, set it now
    if (this.codeEditor && this.codeEditorValueToSet) {
      this.codeEditor.setValue(this.codeEditorValueToSet);
      this.codeEditorValueToSet = null;

      // Force Monaco editor layout after setting content
      setTimeout(() => {
        if (this.codeEditor?.isReady) {
          this.codeEditor['editor']?.layout();
        }
      }, 100);
    }
  }

  ngOnDestroy(): void {
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  private initializeChatMessages(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: "Hi! Welcome to the tool testing playground. I'll help you test your tool by collecting the required parameters.",
      },
    ];
  }

  onSave(): void {
    // Mark all fields as touched to show validation errors
    this.markFormGroupTouched(this.toolForm);

    // Check if form is valid
    if (!this.toolForm.valid) {
      this.showValidationErrorPopup();
      return;
    }

    // Handle create mode only
    if (!this.isEditMode) {
      // For create and clone
      // New payload structure for creating tools
      const createPayload = {
        name: this.toolForm.get('name')?.value,
        description: this.toolForm.get('description')?.value,
        toolConfigs: {
          image: '', // Default empty image for new tools
          tool_class_def: this.toolForm.get('classDefinition')?.value,
          tool_class_name: this.toolForm.get('toolClassName')?.value
        },
        createdBy: this.getUserSignature(),
        modifiedBy: this.getUserSignature()
      };

      console.log('Creating new tool with payload:', createPayload);
      this.toolsService.addNewUserTool(createPayload).subscribe({
        next: (response) => {
          console.log('Tool creation response:', response);
          this.isExecuteMode = true;
          this.showChatInterface = true;
          this.initializeChatMessages();
          this.resetParameterState();

          // Show success popup with API response
          const successMessage = response?.message || 'Tool created successfully!';
          this.showSuccessMessage('Tool Created', successMessage);
        },
        error: (error) => {
          console.error('Error creating tool', error);

          // Show error popup with API response
          const errorMessage = error?.error?.message || error?.message || 'Error creating tool. Please try again.';
          this.showErrorMessage('Creation Failed', errorMessage);
        }
      });
    }
  }

  onUpdate(): void {
    // Mark all fields as touched to show validation errors
    this.markFormGroupTouched(this.toolForm);

    // Check if form is valid
    if (!this.toolForm.valid) {
      this.showValidationErrorPopup();
      return;
    }

    // Handle update mode for both edit and execute
    if (this.isEditMode && this.toolId) {
      // Transform to new API payload structure for update using change_request endpoint
      const updatePayload = {
        id: parseInt(this.toolId.toString()),
        name: this.toolForm.get('name')?.value,
        description: this.toolForm.get('description')?.value,
        createdBy: this.originalToolData?.createdBy || this.getUserSignature(),
        modifiedBy: this.getUserSignature(),
        // Use original timestamp if available, otherwise format new one
        createdAt: this.originalToolData?.createdAt || this.originalToolData?.createTimestamp || this.originalToolData?.createDate || this.formatLocalDateTime(),
        modifiedAt: this.formatLocalDateTime(),
        isDeleted: true, // Set to true for change request
        toolConfigs: {
          image: this.originalToolData?.toolImage || 'TEST1',
          def: this.toolForm.get('classDefinition')?.value,
          CHANGE2: this.toolForm.get('toolClassName')?.value || 'TEST'
        }
      };

      console.log('Update payload being sent:', updatePayload);

      this.toolsService.updateUserTool(updatePayload).subscribe({
        next: (response) => {
          console.log('Tool update response:', response);
          this.isExecuteMode = true;
          this.showChatInterface = true;
          this.initializeChatMessages();
          this.resetParameterState();

          // Show success popup with API response
          const successMessage = response?.message || 'Tool updated successfully!';
          this.showSuccessMessage('Tool Updated', successMessage);
        },
        error: (error) => {
          console.error('Error updating tool:', error);
          console.error('Error details:', error.error);
          console.error('Status:', error.status);

          // Show error popup with API response
          const errorMessage = error?.error?.message || error?.message || 'Error updating tool. Please try again.';
          this.showErrorMessage('Update Failed', errorMessage);
        }
      });
    }
  }



  private extractParameters(): void {
    const toolClassDef = this.toolForm.get('classDefinition')?.value;
    const toolClassName = this.toolForm.get('toolClassName')?.value;
    const toolName = this.toolForm.get('name')?.value;
    if (!toolClassDef || !toolClassName) {
      this.addChatMessage(
        'ai',
        'Error: Tool class definition or class name is missing.',
      );
      return;
    }
    const useCase = 'PARAMETER_EXTRACTOR';
    const useCaseIdentifier =
      'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST';

    this.isProcessingChat = true;
    this.addChatMessage('ai', 'Analyzing your tool to extract parameters...');

    this.promptGenerateService
      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)
      .subscribe({
        next: (response: any) => {
          console.log('Parameter extraction response:', response);
          try {
            const parametersText = response?.response?.choices?.[0]?.text;
            if (!parametersText) {
              throw new Error('No parameters found in response');
            }
            const parametersObj = JSON.parse(parametersText);
            const parameters: ExtractedParameter[] = Object.keys(
              parametersObj,
            ).map((key) => ({
              name: key,
              type: parametersObj[key],
            }));

            if (parameters.length === 0) {
              this.isProcessingChat = false;
              this.addChatMessage(
                'ai',
                'No parameters found for this tool. The tool might not require any input parameters.',
              );
              return;
            }

            this.parameterState = {
              parameters: parameters,
              currentParameterIndex: 0,
              collectedInputs: {},
              isCollecting: true,
            };

            this.isProcessingChat = false;
            this.promptForNextParameter();
          } catch (error) {
            console.error('Error parsing parameters:', error);
            this.isProcessingChat = false;
            this.addChatMessage(
              'ai',
              'Error: Failed to parse extracted parameters. Please check your tool definition.',
            );
          }
        },
        error: (error) => {
          console.error('Parameter extraction error', error);
          this.isProcessingChat = false;
          this.addChatMessage(
            'ai',
            'Error: Failed to extract parameters from your tool. Please check your tool definition.',
          );
        },
      });
  }

  private promptForNextParameter(): void {
    if (
      this.parameterState.currentParameterIndex <
      this.parameterState.parameters.length
    ) {
      const currentParam =
        this.parameterState.parameters[
          this.parameterState.currentParameterIndex
        ];
      const message = `Please enter input for parameter "${currentParam.name}" (type: ${currentParam.type}):`;
      this.addChatMessage('ai', message);
    }
  }

  private addChatMessage(from: 'ai' | 'user', text: string): void {
    this.chatMessages = [...this.chatMessages, { from, text }];
  }

  handleChatMessage(message: string): void {
    if (!this.isExecuteMode) return;
    this.addChatMessage('user', message);
    if (this.waitingForRestartConfirmation) {
      if (message.toLowerCase().trim() === 'yes') {
        this.waitingForRestartConfirmation = false;
        this.resetParameterState();
        this.addChatMessage(
          'ai',
          'Restarting the parameter collection process...',
        );
        this.extractParameters();
      } else {
        this.waitingForRestartConfirmation = false;
        this.addChatMessage(
          'ai',
          'Okay, feel free to ask if you need any further assistance.',
        );
      }
    } else if (this.parameterState.isCollecting) {
      this.handleParameterInput(message);
    }
    else {
      this.addChatMessage('ai', 'Hello');
    }
  }

  private handleParameterInput(input: string): void {
    const currentParam =
      this.parameterState.parameters[this.parameterState.currentParameterIndex];
    let processedInput: any = input;

    try {
      switch (currentParam.type) {
        case 'number':
          processedInput = parseFloat(input);
          if (isNaN(processedInput)) {
            this.addChatMessage(
              'ai',
              `Invalid number format. Please enter a valid number for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'boolean':
          const lowerInput = input.toLowerCase();
          if (
            lowerInput === 'true' ||
            lowerInput === '1' ||
            lowerInput === 'yes'
          ) {
            processedInput = true;
          } else if (
            lowerInput === 'false' ||
            lowerInput === '0' ||
            lowerInput === 'no'
          ) {
            processedInput = false;
          } else {
            this.addChatMessage(
              'ai',
              `Invalid boolean format. Please enter true/false for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'object':
          try {
            processedInput = JSON.parse(input);
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid JSON format. Please enter a valid JSON object for "${currentParam.name}":`,
            );
            return;
          }
          break;
        case 'array':
          try {
            processedInput = JSON.parse(input);
            if (!Array.isArray(processedInput)) {
              throw new Error('Not an array');
            }
          } catch {
            this.addChatMessage(
              'ai',
              `Invalid array format. Please enter a valid JSON array for "${currentParam.name}":`,
            );
            return;
          }
          break;
      }
      this.parameterState.collectedInputs[currentParam.name] = processedInput;
      this.parameterState.currentParameterIndex++;
      if (
        this.parameterState.currentParameterIndex >=
        this.parameterState.parameters.length
      ) {
        this.executeToolWithParameters();
      } else {
        this.promptForNextParameter();
      }
    } catch (error) {
      console.error('Error processing parameter input:', error);
      this.addChatMessage(
        'ai',
        `Error processing input for "${currentParam.name}". Please try again:`,
      );
    }
  }

  private executeToolWithParameters(): void {
    this.parameterState.isCollecting = false;
    this.isProcessingChat = true;

    this.addChatMessage(
      'ai',
      'All parameters collected! Executing your tool...',
    );

    const payload = {
      class_definition: this.toolForm.get('classDefinition')?.value,
      class_name: this.toolForm.get('toolClassName')?.value,
      inputs: this.parameterState.collectedInputs,
    };

    this.toolsService.testTool(payload).subscribe({
      next: (response: any) => {
        console.log('Tool execution response:', response);
        this.isProcessingChat = false;

        if (response.status === 'success') {
          this.addChatMessage(
            'ai',
            `Tool executed successfully! Output: ${response.output}`,
          );
        } else {
          this.addChatMessage(
            'ai',
            `Tool execution failed: ${response.detail || 'Unknown error'}`,
          );
        }
        this.waitingForRestartConfirmation = true;
        setTimeout(() => {
          this.addChatMessage(
            'ai',
            'Would you like to test the tool again with different parameters? (Type "yes" to restart or anything else to continue)',
          );
        }, 1000);
      },
      error: (error) => {
        console.error('Tool execution error:', error);
        this.isProcessingChat = false;
        this.addChatMessage(
          'ai',
          `Tool execution failed: ${error?.error?.message || 'Unknown error occurred'}`,
        );
      },
    });
  }

  onExit(): void {
    // Always navigate back to tools landing page regardless of mode
    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    console.log('Exiting create-tools component, navigating to tools landing page');
    this.router.navigate(['/libraries/tools'], {
      queryParams: { page: pageNumber },
    });
  }

  private resetParameterState(): void {
    this.parameterState = {
      parameters: [],
      currentParameterIndex: 0,
      collectedInputs: {},
      isCollecting: false,
    };
  }

  getControl(name: string): FormControl {
    return this.toolForm.get(name) as FormControl;
  }

  // Custom validator for whitespace
  noWhitespaceValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (value && typeof value === 'string' && value.trim().length === 0) {
      return { invalidInput: true };
    }
    return null;
  }

  // // Custom validator for class definition
  // validateClassDef(control: AbstractControl): ValidationErrors | null {
  //   const className = this.toolForm?.controls['toolClassName'].value?.trim();
  //   const classDef = control.value;
  //   if (!className) return null;
  //   const classRegex = new RegExp(`class\\s+${className}\\s*\\(\\s*BaseTool\\s*\\)`);
  //   if (!classRegex.test(classDef)) {
  //     return { invalidClass: { invalidClass: `Please Define: "class ${className}(BaseTool)"` }};
  //   }
  //   if (!classDef.includes('_run')) {
  //     return { invalidClass: { invalidClass: `Please Define: "_run" Method` }};
  //   }
  //   return null;
  // }

  // Helper method to mark all form fields as touched
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Show validation error popup
  showValidationErrorPopup(): void {
    const errors: string[] = [];

    // Check each field for errors
    Object.keys(this.toolForm.controls).forEach(fieldName => {
      const error = this.getFieldError(fieldName);
      if (error) {
        const fieldLabel = this.getFieldLabel(fieldName);
        errors.push(`${fieldLabel}: ${error}`);
      }
    });

    if (errors.length > 0) {
      this.popupTitle = 'Validation Error';
      this.popupMessage = errors.join('\n\n');
      this.showErrorPopup = true;
    }
  }

  // Get field label for error messages
  getFieldLabel(fieldName: string): string {
    switch (fieldName) {
      case 'name': return this.labels.toolName;
      case 'toolClassName': return this.labels.toolClassName;
      case 'description': return this.labels.description;
      case 'classDefinition': return this.labels.toolClassDefinition;
      default: return fieldName;
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.toolForm.get(fieldName);
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        if (fieldName === 'name') return this.labels.errorToolNameRequired;
        if (fieldName === 'toolClassName') return this.labels.errorToolClassNameRequired;
        if (fieldName === 'description') return this.labels.errorDescriptionRequired;
        if (fieldName === 'classDefinition') return this.labels.errorToolConfigRequired;
        return this.labels.errorRequired || 'This field is required';
      }
      if (field.errors?.['invalidInput']) {
        return 'Please enter a valid value (whitespace only is not allowed)';
      }
      if (field.errors?.['pattern']) {
        if (fieldName === 'toolClassName') {
          return 'Please enter a valid class name (must start with uppercase letter and contain only letters and numbers)';
        }
      }
      if (field.errors?.['invalidClass']) {
        return field.errors['invalidClass'].invalidClass;
      }
      // Add more error types as needed
    }
    return '';
  }

  validateCode = (): void => {
    this.validateTool();
  };

  public validateTool(): void {
    this.showValidationOutput = false;
    this.validationOutput = '';
    const useCase = 'VALIDATE_TOOLS';
    const useCaseIdentifier =
      'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
    const toolClassDef = this.toolForm.controls['classDefinition'].value;
    this.promptGenerateService
      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)
      .subscribe({
        next: (res: any) => {
          let responseText = res?.response?.choices?.[0]?.text;
          if (!responseText) {
            this.validationOutput = 'Unable to validate, please try again.';
            this.showValidationOutput = true;
            if (this.codeEditor) this.codeEditor.hideProcessingLoader();
            return;
          }
          // Remove markdown code block if present
          responseText = responseText
            .replace(/```json\n?/, '')
            .replace(/```\n?$/, '');
          try {
            const parsed = JSON.parse(responseText);
            // Format as plain text
            let formatted = '';
            if (
              parsed.issues &&
              Array.isArray(parsed.issues) &&
              parsed.issues.length > 0
            ) {
              parsed.issues.forEach((issue: any, idx: number) => {
                formatted += `Issue ${idx + 1}:\n`;
                formatted += `  Type: ${issue.type}\n`;
                formatted += `  Description: ${issue.description}\n`;
                formatted += `  Severity: ${issue.severity}\n`;
                formatted += `  Line Number: ${issue.line_number}\n`;
                formatted += `  Suggestion: ${issue.suggestion}\n\n`;
              });
            } else {
              formatted = 'No issues found.';
            }
            this.validationOutput = formatted;
          } catch (e) {
            this.validationOutput = responseText;
          }
          this.showValidationOutput = true;
          if (this.codeEditor) this.codeEditor.hideProcessingLoader();
        },
        error: (e) => {
          this.validationOutput =
            'Error: ' + (e?.error?.message || 'Unknown error');
          this.showValidationOutput = true;
          if (this.codeEditor) this.codeEditor.hideProcessingLoader();
        },
      });
  }

  loadToolData(toolId: number): void {
    this.toolsService.getUserToolDetails(toolId).subscribe(
      (response) => {
        console.log('API response:', response); // Debug log
        const tool = response.tools && response.tools[0];
        console.log('Tool for patching:', tool); // Debug log
        console.log('Tool ID:', tool?.toolId);
        if (tool) {
          this.originalToolData = tool;
          console.log('Original tool data structure:', JSON.stringify(tool, null, 2));
          this.toolForm.patchValue({
            name: this.isCloneMode ? '' : (tool.toolName || ''),
            description: tool.toolDescription || '',
            toolClassName: tool.toolClassName || '',
            classDefinition: tool.toolClassDef || '',
          });
          // Set code editor value, but only if editor is ready
          if (this.codeEditor) {
            this.codeEditor.setValue(tool.toolClassDef || '');
            // Force Monaco editor layout after setting content
            setTimeout(() => {
              if (this.codeEditor?.isReady) {
                this.codeEditor['editor']?.layout();
              }
            }, 100);
          } else {
            this.codeEditorValueToSet = tool.toolClassDef || '';
          }

          // Auto-start parameter extraction when in execute mode
          if (this.isExecuteMode) {
            console.log('Auto-starting parameter extraction in execute mode...');
            // Small delay to ensure form is fully populated and UI is ready
            setTimeout(() => {
              this.extractParameters();
            }, 500);
          }
        }
      },
      (error) => {
        console.error('Error loading tool data:', error);
      },
    );
  }

  //Drop Down
  promptOptions: DropdownOption[] = [
    { value: 'default', name: 'Choose Prompt' },
    { value: 'ruby-developer', name: 'Senior Ruby Developer' },
    { value: 'python-developer', name: 'Python Developer' },
    { value: 'data-scientist', name: 'Data Scientist' },
    { value: 'frontend-developer', name: 'Frontend Developer' },
  ];

  onPromptChanged(option: DropdownOption) {
    console.log('Prompt changed in parent:', option);
    // your logic to handle selected prompt
  }

  onEditorAction(idx: number) {
    if (!this.codeEditor) return;
    if (idx === 0) this.codeEditor.selectAll();
    if (idx === 1) this.codeEditor.clear();
  }

  handleApproval() {
    if (!this.isEditMode || !this.toolId) {
      console.error('Cannot send for approval: Tool must be saved first');
      return;
    }

    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');
    const pageNumber = returnPage ? parseInt(returnPage) : 1;

    // Create approval payload with the exact structure required
    const approvalPayload = {
      id: parseInt(this.toolId.toString()),
      name: this.toolForm.get('name')?.value,
      description: this.toolForm.get('description')?.value,
      createdBy: this.originalToolData?.createdBy || this.getUserSignature(),
      modifiedBy: this.getUserSignature(), // Use current user signature for modifiedBy
      createdAt: this.originalToolData?.createTimestamp || this.formatLocalDateTime(),
      "put ": this.formatLocalDateTime(), // Using LocalDateTime format without timezone
      isDeleted: true, // Set to true for approval request
      toolConfigs: {
        image: this.originalToolData?.toolImage || '',
        def: this.toolForm.get('classDefinition')?.value,
        CHANGE2: this.toolForm.get('toolClassName')?.value || 'TEST'
      }
    };

    console.log('Sending for approval with payload:', approvalPayload);
    console.log('Approval payload structure:', JSON.stringify(approvalPayload, null, 2));
    console.log('API Endpoint: /v2/api/admin/ava/force/da/userTools/change_request');

    this.toolsService.updateUserTool(approvalPayload).subscribe(
      (response) => {
        console.log('Approval request successful:', response);
        // You can add a success message or redirect here
        this.router.navigate(['/libraries/tools'], {
          queryParams: { page: pageNumber },
        });
      },
      (error) => {
        console.error('Error sending for approval:', error);
        console.error('Error details:', error.error);
        console.error('Status:', error.status);
        console.error('Status text:', error.statusText);
      },
    );
  }

  // Popup handling methods
  showSuccessMessage(title: string, message: string): void {
    console.log('showSuccessMessage called:', title, message);
    this.popupTitle = title;
    this.popupMessage = message;
    this.showSuccessPopup = true;
    this.shouldCallExtractParams = true;
    console.log('showSuccessPopup set to:', this.showSuccessPopup);
  }

  showErrorMessage(title: string, message: string): void {
    console.log('showErrorMessage called:', title, message);
    this.popupTitle = title;
    this.popupMessage = message;
    this.showErrorPopup = true;
    this.shouldCallExtractParams = false;
    console.log('showErrorPopup set to:', this.showErrorPopup);
  }

  closeSuccessPopup(): void {
    console.log('closeSuccessPopup called');
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  closeErrorPopup(): void {
    console.log('closeErrorPopup called');
    this.showErrorPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  // Handle success popup confirmation - call extract params API
  onSuccessConfirm(): void {
    console.log('onSuccessConfirm called, shouldCallExtractParams:', this.shouldCallExtractParams);
    this.closeSuccessPopup();

    if (this.shouldCallExtractParams) {
      console.log('Calling extractParameters...');
      this.extractParameters();
    }
  }

  // Get tool name for display in playground
  getToolDisplayName(): string {
    const toolName = this.toolForm.get('name')?.value;
    return toolName || 'Tool Name';
  }
}
