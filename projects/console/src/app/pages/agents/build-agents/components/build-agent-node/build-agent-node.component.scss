.build-agent-node {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.1s ease;
  display: inline-block;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  width: auto;
  height: 48px;
  &.selected {
    z-index: 10;
  }
  &.dragging {
    z-index: 20;
    opacity: 0.9;
    transition: none;
  }
  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &.execute-mode {
    width: 55px;
    height: 55px;
    background: white;
    border-radius: 40px;
    border: 7px solid white; // White border around the icon
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    position: absolute !important; // Force absolute positioning
    transform: none !important; // Override any CDK Drag transforms
    left: var(--node-x, 0px) !important; // Use CSS variables for positioning
    top: var(--node-y, 0px) !important;
  }

  .node-content-execute {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    border: 7px solid white; // White border around the icon
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: none; // Remove transition to prevent flickering
    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
      filter: brightness(0) invert(1);
    }
    lucide-icon {
      color: white;
    }
  }
  &[data-node-type="prompt"] .node-content-execute {
    background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-execute {
    background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);
  }

  &[data-node-type="knowledge"] .node-content-execute {
    background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);
  }

  &[data-node-type="tool"] .node-content-execute {
    background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);
  }

  &[data-node-type="guardrail"] .node-content-execute {
    background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);
  }

  .execute-tooltip {
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9999;
    margin-right: 10px;

    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: white;

    padding: 10px 14px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    font-size: 12px;
    font-weight: 500;
    line-height: 1.4;
    text-align: left;

    min-width: 150px;
    max-width: 400px;
    width: auto;
    max-height: 300px;
    height: auto;

    white-space: pre-line;
    overflow-y: auto;
    overflow-x: hidden;
    word-wrap: break-word;
    text-overflow: clip;
    display: block;

    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.4);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.6);
      }
    }

    &::-webkit-scrollbar-corner {
      background: rgba(255, 255, 255, 0.1);
    }

    &::after,
    &::before {
      content: "";
      position: absolute;
      left: 100%;
      top: 50%;
      transform: translateY(-50%);
    }

    &::after {
      border-style: solid;
      border-width: 8px 0 8px 10px;
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.85);
      filter: drop-shadow(1px 0 2px rgba(0, 0, 0, 0.2));
      margin-left: -1px;
    }

    &::before {
      border-style: solid;
      border-width: 9px 0 9px 11px;
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.3);
      z-index: -1;
      margin-left: -2px;
    }
  }

  .node-content-build {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 4px 16px 4px 4px;
    min-width: fit-content;
    height: auto;
    position: relative;
    transition: none;

    &::before {
      content: "";
      position: absolute;
      left: 34px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      background: #9ca3af;
      border-radius: 50%;
      z-index: 0;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .node-icon-section {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      z-index: 2;
      position: relative;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
        filter: brightness(0) invert(1);
      }
    }

    .node-label-section {
      display: flex;
      align-items: center;
      padding-left: 12px;
      height: 100%;

      .node-label {
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
        line-height: 1;
        max-width: 140px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: box-shadow 0.1s ease;

      &::before {
        opacity: 1;
        transition: opacity 0.1s ease;
      }
    }
  }

  &[data-node-type="prompt"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);
  }

  &[data-node-type="knowledge"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);
  }

  &[data-node-type="tool"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);
  }

  &[data-node-type="guardrail"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);
  }

  .delete-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 10;
    transition:
      background 0.2s ease,
      transform 0.2s ease;

    &:hover {
      background: #dc2626;
      transform: scale(1.1);
    }

    svg {
      width: 10px;
      height: 10px;
    }
  }
}