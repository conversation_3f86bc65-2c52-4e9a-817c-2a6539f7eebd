<div class="build-agents-container">
  <!-- Header Navigation -->
  <div class="header-nav">
    <!-- Main Content Area -->
    <div class="main-content" [class.execute-mode]="isExecuteMode">
      <!-- Full Width Canvas Area -->
      <div class="canvas-area" [class.execute-mode]="isExecuteMode">
        <!-- Configure Agent Floating Panel - Visible in both build and execute modes -->
        <div
          class="configure-agent-panel"
          [ngClass]="{ 'p-3': !isSidebarCollapsed }"
          [class.collapsed]="isSidebarCollapsed"
        >
          <div class="panel-header" (click)="toggleSidebar()">
            <h3>Configure Agent</h3>
            <ava-icon
              [iconName]="isSidebarCollapsed ? 'ChevronDown' : 'ChevronUp'"
              iconSize="16"
              iconColor="var(--text-secondary)"
            >
            </ava-icon>
          </div>

          <!-- Panel Content -->
          <div class="panel-content" [class.hidden]="isSidebarCollapsed">
            <!-- Tool Items Section -->
            <div class="tools-section">
              <!-- Ava Tabs with Custom PNG Icons -->
              <div class="custom-tabs-container">
                <app-custom-tabs
                  [tabs]="customTabs"
                  [activeTab]="activeTab"
                  [variant]="'icon'"
                  (tabChange)="onCustomTabChange($event)"
                  class="builder-custom-tabs"
                >
                </app-custom-tabs>
              </div>

              <!-- Search Section -->
              <div class="search-section">
                <form [formGroup]="searchForm">
                  <ava-textbox
                    [placeholder]="'Search ' + activeTab"
                    hoverEffect="glow"
                    pressedEffect="solid"
                    formControlName="search"
                  >
                    <ava-icon
                      slot="icon-start"
                      iconName="search"
                      [iconSize]="16"
                      iconColor="var(--color-brand-primary)"
                    >
                    </ava-icon>
                  </ava-textbox>
                </form>
              </div>

              <!-- Tool Items List -->
              <div class="tools-list">
                <!-- Show tools when available -->
                <div
                  *ngFor="let tool of filteredTools"
                  class="tool-item"
                  draggable="true"
                  (dragstart)="onDragStart($event, tool)"
                >
                  <!-- Header with icon, name and user count in one line -->
                  <div class="tool-header">
                    <div class="tool-icon-box">
                      <!-- Use Lucide icons for all types including prompts -->
                      <ava-icon
                        *ngIf="tool.type === 'prompt'"
                        iconName="FileText"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'model'"
                        iconName="Box"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'knowledge'"
                        iconName="BookOpen"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'tool'"
                        iconName="Wrench"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                      <ava-icon
                        *ngIf="tool.type === 'guardrail'"
                        iconName="Swords"
                        iconSize="20"
                        iconColor="var(--color-brand-primary)"
                      >
                      </ava-icon>
                    </div>
                    <h4 class="tool-name">{{ tool.name }}</h4>
                    <div class="tool-count">
                      <ava-icon
                        iconName="Users"
                        iconSize="16"
                        iconColor="#9CA3AF"
                      >
                      </ava-icon>
                      <span class="count-text">120</span>
                    </div>
                  </div>

                  <!-- Description -->
                  <p class="tool-description" *ngIf="tool.type !== 'prompt'">{{ tool.description }}</p>
                  <!-- Prompt Description for prompts -->
                  <p class="tool-description" *ngIf="tool.type === 'prompt'">{{ tool['promptDescription'] || tool.description }}</p>

                  <!-- Preview button -->
                  <div class="tool-actions">
                    <ava-button
                      label="Preview"
                      size="small"
                      [pill]="true"
                      variant="secondary"
                      (userClick)="onItemPreview(tool)"
                      class="preview-btn"
                    >
                    </ava-button>
                  </div>
                </div>

                <!-- Show no results message when search returns empty and search query exists -->
                <div
                  *ngIf="
                    filteredTools.length === 0 &&
                    searchQuery &&
                    searchQuery.trim().length > 0
                  "
                  class="no-results-message"
                >
                  <div class="no-results-content">
                    <ava-icon
                      iconName="Search"
                      iconSize="24"
                      iconColor="#9CA3AF"
                    >
                    </ava-icon>
                    <p>
                      No {{ activeTab }} found matching your search criteria
                    </p>
                  </div>
                </div>
              </div>

              <!-- Create New Item Button -->
              <div class="create-tool-section">
                <ava-button
                  [label]="'Create New ' + getActiveTabLabel()"
                  variant="primary"
                  [customStyles]="{
                    background:
                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                    '--button-effect-color': '33, 90, 214',
                    'border-radius': '8px',
                  }"
                  size="large"
                  iconName="Plus"
                  iconColor="white"
                  (userClick)="onCreateNewItem()"
                  [width]="'100%'"
                >
                </ava-button>
              </div>
            </div>
          </div>
        </div>

        <div class="editor-canvas">
          <app-canvas-board
            [nodes]="canvasNodes"
            [edges]="canvasEdges"
            [navigationHints]="[]"
            [fallbackMessage]="
              'Drag tools from the left panel to build your agent'
            "
            [primaryButtonText]="primaryButtonText"
            [showToolbar]="true"
            [enableConnections]="true"
            [enablePan]="true"
            [enableZoom]="false"
            [isExecuteMode]="isExecuteMode"
            [mouseInteractionsEnabled]="!isExecuteMode"
            [showHeaderInputs]="true"
            [initialAgentName]="agentName"
            [initialAgentDetails]="agentDetail"
            [initialMetadata]="agentMetadata"
            [inputFieldsConfig]="{
              agentName: {
                enabled: !isFieldsDisabled,
                placeholder: 'Agent Name',
                required: true,
              },
              metadata: {
                enabled: !isFieldsDisabled,
                label: 'Metadata Info',
                statusText: {
                  saved: 'Metadata Information saved',
                  notSaved: 'Metadata Information not saved',
                },
              },
              agentDetails: {
                enabled: true,
                label: 'Agent Details',
                namePlaceholder: 'Enter agent name',
                detailPlaceholder: 'Enter agent description',
              },
              agentTypeTag: {
                enabled: true,
                value: currentAgentType,
                showInAgentsBuilderOnly: true,
              },
            }"
            (canvasDropped)="onCanvasDropped($event)"
            (nodeSelected)="onNodeSelected($event)"
            (nodeDoubleClicked)="onNodeDoubleClicked($event)"
            (nodeMoved)="onNodeMoved($event)"
            (nodeRemoved)="onDeleteNode($event)"
            (connectionCreated)="onConnectionCreated($event)"
            (stateChanged)="onCanvasStateChanged($event)"
            (agentNameChanged)="onAgentNameChanged($event)"
            (metadataChanged)="onMetadataChanged($event)"
            (agentDetailsChanged)="onAgentDetailsChanged($event)"
            (primaryButtonClicked)="onPrimaryButtonClick()"
          >
            <!-- Node template for rendering agent nodes -->
            <ng-template
              #nodeTemplate
              let-node
              let-selected="selected"
              let-onDelete="onDelete"
              let-onMove="onMove"
              let-onSelect="onSelect"
              let-onDoubleClick="onDoubleClick"
              let-onStartConnection="onStartConnection"
              let-mouseInteractionsEnabled="mouseInteractionsEnabled"
              let-canvasMode="canvasMode"
            >
              <app-build-agent-node
                [node]="node"
                [selected]="selected"
                [mouseInteractionsEnabled]="mouseInteractionsEnabled"
                [canvasMode]="canvasMode"
                [executeNodeData]="getExecuteNodeData(node)"
                (deleteNode)="onDelete($event)"
                (moveNode)="onMove($event)"
                (nodeSelected)="onSelect($event)"
                (nodeDoubleClicked)="onDoubleClick($event)"
                (startConnection)="onStartConnection($event)"
              >
              </app-build-agent-node>
            </ng-template>
          </app-canvas-board>
        </div>
      </div>

      <!-- Playground Component - Shows when in execute mode -->
      <div class="playground-area" *ngIf="isExecuteMode && showChatInterface">
        <button
          class="exit-execute-btn"
          (click)="onExitExecuteMode()"
          title="Exit Execute Mode"
        ></button>

        <app-playground
          [messages]="chatMessages"
          [isLoading]="isProcessingChat"
          [agentType]="currentAgentType"
          [showChatInteractionToggles]="true"
          [showAiPrincipleToggle]="true"
          [showApprovalButton]="showApprovalButton"
          [showDropdown]="false"
          [showAgentNameInput]="true"
          [showFileUploadButton]="true"
          [displayedAgentName]="agentName"
          [agentNamePlaceholder]="'Current Agent Name'"
          (promptChange)="onPromptChanged($event)"
          (messageSent)="handleChatMessage($event)"
          (conversationalToggle)="onPlaygroundConversationalToggle($event)"
          (templateToggle)="onPlaygroundTemplateToggle($event)"
          (filesSelected)="onFilesSelected($event)"
          (approvalRequested)="onApprovalRequested()"
        >
        </app-playground>
      </div>

      <!-- Preview Panel -->
      <div
        class="preview-panel"
        *ngIf="showPreview"
        [class.visible]="showPreview"
      >
        <div class="preview-header">
          <h3 class="preview-title">
            {{ previewData?.title || "Item Details" }}
          </h3>
          <button
            class="close-preview-btn"
            (click)="closePreview()"
            type="button"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div class="preview-content">
          <!-- Loading State -->
          <div *ngIf="isLoadingPreview" class="preview-loading">
            <div class="loading-spinner"></div>
            <p>Loading details...</p>
          </div>

          <!-- Error State -->
          <div *ngIf="previewData?.error" class="preview-error">
            <p>{{ previewData.error }}</p>
          </div>

          <!-- Model Preview -->
          <div
            *ngIf="previewData?.type === 'model' && previewData?.data"
            class="model-preview"
          >
            <div class="preview-field">
              <label>Model Name:</label>
              <span>{{ previewData.data.modelDeploymentName }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{
                previewData.data.modelDescription || "No description available"
              }}</span>
            </div>
            <div class="preview-field">
              <label>Model Type:</label>
              <span>{{ previewData.data.modelType }}</span>
            </div>
            <div class="preview-field">
              <label>AI Engine:</label>
              <span>{{ previewData.data.aiEngine }}</span>
            </div>
            <div class="preview-field">
              <label>Created:</label>
              <span>{{ previewData.data.date | date: "medium" }}</span>
            </div>
            <div class="preview-field">
              <label>Model ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
          </div>

          <!-- Tool Preview -->
          <div
            *ngIf="previewData?.type === 'tool' && previewData?.data"
            class="tool-preview"
          >
            <div class="preview-field">
              <label>Tool Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.className">
              <label>Class Name:</label>
              <span>{{ previewData.data.className }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.createdBy">
              <label>Created by:</label>
              <span>{{ previewData.data.createdBy }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.createdOn">
              <label>Created on:</label>
              <span>{{ previewData.data.createdOn | date: "medium" }}</span>
            </div>
            <div class="preview-field">
              <label>Approved:</label>
              <span>{{ previewData.data.isApproved ? "Yes" : "No" }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.functionality">
              <label>Tool Definition:</label>
              <pre class="content-preview">{{
                previewData.data.functionality
              }}</pre>
            </div>
          </div>

          <!-- Prompt Preview -->
          <div
            *ngIf="previewData?.type === 'prompt' && previewData?.data"
            class="prompt-preview"
          >
            <div class="preview-field">
              <label>Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Role:</label>
              <span>{{ previewData.data.role }}</span>
            </div>
            <div class="preview-field">
              <label>Goal:</label>
              <span>{{ previewData.data.goal }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.promptDescription">
              <label>Prompt Description:</label>
              <span>{{ previewData.data.promptDescription }}</span>
            </div>
            <div class="preview-field">
              <label>Backstory:</label>
              <span>{{ previewData.data.backstory }}</span>
            </div>
            <div class="preview-field">
              <label>Expected Output:</label>
              <span>{{ previewData.data.expectedOutput }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryName">
              <label>Category:</label>
              <span>{{ previewData.data.categoryName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.domainName">
              <label>Domain:</label>
              <span>{{ previewData.data.domainName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.type">
              <label>Type:</label>
              <span>{{ previewData.data.type }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.updatedAt">
              <label>Updated:</label>
              <span>{{ previewData.data.updatedAt | date: "medium" }}</span>
            </div>
          </div>

          <!-- Knowledge Base Preview -->
          <div
            *ngIf="previewData?.type === 'knowledge' && previewData?.data"
            class="knowledge-preview"
          >
            <div class="preview-field">
              <label>Collection ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
            <div class="preview-field">
              <label>Retriever Type:</label>
              <span>{{ previewData.data.retrieverType }}</span>
            </div>
            <div class="preview-field">
              <label>Total Files:</label>
              <span>{{ previewData.data.totalFiles }}</span>
            </div>
            <div
              class="preview-field"
              *ngIf="
                previewData.data.files && previewData.data.files.length > 0
              "
            >
              <label>Files:</label>
              <div class="files-list">
                <div
                  *ngFor="let file of previewData.data.files"
                  class="file-item"
                >
                  <strong>{{ file.fileName }}</strong>
                  <small
                    >{{ file.fileSizeBytes | number }} bytes -
                    {{ file.uploadDate | date: "short" }}</small
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Guardrail Preview -->
          <div
            *ngIf="previewData?.type === 'guardrail' && previewData?.data"
            class="guardrail-preview"
          >
            <div class="preview-field">
              <label>Label ID:</label>
              <span>{{ previewData.data.id }}</span>
            </div>
            <div class="preview-field">
              <label>Name:</label>
              <span>{{ previewData.data.name }}</span>
            </div>
            <div class="preview-field">
              <label>Description:</label>
              <span>{{ previewData.data.description }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.labelCode">
              <label>Label Code:</label>
              <span>{{ previewData.data.labelCode }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryName">
              <label>Category:</label>
              <span>{{ previewData.data.categoryName }}</span>
            </div>
            <div class="preview-field" *ngIf="previewData.data.categoryId">
              <label>Category ID:</label>
              <span>{{ previewData.data.categoryId }}</span>
            </div>
            <!-- Show any additional fields that might be available -->
            <div
              class="preview-field"
              *ngFor="let field of getAdditionalFields(previewData.data)"
            >
              <label>{{ field.key }}:</label>
              <span>{{ field.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Success Popup -->
  <ava-popup
    [show]="showSuccessPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="check-circle"
    iconColor="#28a745"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="true"
    [confirmButtonLabel]="'OK'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#28a745'"
    (confirm)="onSuccessConfirm()"
    (closed)="closeSuccessPopup()"
  >
  </ava-popup>

  <!-- Error Popup -->
  <ava-popup
    [show]="showErrorPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-circle"
    iconColor="#dc3545"
    [showClose]="true"
    [showCancel]="false"
    [showConfirm]="true"
    [confirmButtonLabel]="'OK'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#dc3545'"
    (confirm)="closeErrorPopup()"
    (closed)="closeErrorPopup()"
  >
  </ava-popup>

  <!-- Warning Popup -->
  <ava-popup
    [show]="showWarningPopup"
    [title]="popupTitle"
    [message]="popupMessage"
    [showHeaderIcon]="true"
    headerIconName="alert-triangle"
    iconColor="#ffc107"
    [showClose]="true"
    [showCancel]="true"
    [showConfirm]="true"
    [confirmButtonLabel]="'Continue'"
    [cancelButtonLabel]="'Cancel'"
    [confirmButtonVariant]="'primary'"
    [confirmButtonBackground]="'#ffc107'"
    (confirm)="onWarningConfirm()"
    (cancel)="onWarningCancel()"
    (closed)="closeWarningPopup()"
  >
  </ava-popup>

  <!-- Approval Confirmation Popup removed - approval now handled through canvas button -->
</div>
