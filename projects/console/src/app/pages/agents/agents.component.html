<div id="agents-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-8 col-lg-9 col-xl-10 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Agents"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-dropdown
        dropdownTitle="choose agent"
        [options]="agentsOptions"
        (selectionChange)="onSelectionChange($event)"
      >
      </ava-dropdown>
    </div>
  </div>

  <div class="filter-buttons-section ">
    <!-- <ava-button
      label="All"
      variant="secondary"
      [pill]="true"
      height="30px"
      [class.active]="selectedFilter === 'all'"
      (userClick)="onFilterChange('all')"
      class="filter-btn"
    >
    </ava-button> -->
    <ava-button
      label="Individual"
      [variant]="selectedFilter === 'individual' ? 'primary' : 'secondary'"
      [pill]="true"
      size = 'small'
      [class.active]="selectedFilter === 'individual'"
      (userClick)="onFilterChange('individual')"
      class="filter-btn"
    >
    </ava-button>
    <ava-button
      label="Collaborative"
      [variant]="selectedFilter === 'collaborative' ? 'primary' : 'secondary'"
      [pill]="true"
      size = 'small'
      [class.active]="selectedFilter === 'collaborative'"
      (userClick)="onFilterChange('collaborative')"
      class="filter-btn"
    >
    </ava-button>
  </div>

  <div id="prompts-card-container" class="row g-3">
    <ava-text-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      [title]="agentsConfig.labels.createAgent"
      (cardClick)="onCreateAgent()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf="!isLoading && displayedAgents.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">No Agents found matching your criteria</h5>
      </div>
    </div>

    <ng-container *ngFor="let agent of isLoading && displayedAgents.length === 0 ? cardSkeletonPlaceholders : displayedAgents">
      <ava-text-card
        class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
        [type]="'prompt'"
        [title]="!isLoading ? agent.title : ''"
        [description]="!isLoading ? agent.description : ''"
        [iconList]="!isLoading ? iconList : ''"
        iconColor="#144692"
        (iconClick)="!isLoading ? onIconClicked($event, agent.id) : ''"
        [headerIcons]="!isLoading ? getHeaderIcons(agent) : []"
        [footerIcons]="!isLoading ? getFooterIcons(agent) : []"
        [isLoading]="isLoading"
      >
      </ava-text-card>
    </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredAgents.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <!-- Client-side pagination for individual agents -->
      <app-page-footer
        *ngIf="selectedFilter === 'individual'"
        [totalItems]="filteredAgents.length + 1"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>

      <!-- Server-side pagination for collaborative agents -->
      <app-page-footer
        *ngIf="selectedFilter === 'collaborative'"
        [totalItems]="collaborativeTotalRecords"
        [currentPage]="collaborativeCurrentPage"
        [itemsPerPage]="collaborativeRecordsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>

<!-- Delete Confirmation Popup -->
<ava-popup
  [show]="showDeletePopup"
  title="Delete Agent?"
  [message]="
    'Are you sure you want to delete ' + (agentToDelete?.name || '') + '?'
  "
  [showHeaderIcon]="true"
  headerIconName="trash"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Delete'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="onConfirmDelete()"
  (cancel)="closeDeletePopup()"
  (closed)="closeDeletePopup()"
>
</ava-popup>

<!-- Success Popup -->
<ava-popup
  [show]="showSuccessPopup"
  [title]="successPopupTitle"
  [message]="successPopupMessage"
  [showHeaderIcon]="true"
  headerIconName="check-circle"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  [showConfirm]="true"
  [confirmButtonLabel]="'OK'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#28a745'"
  (confirm)="onSuccessConfirm()"
  (closed)="closeSuccessPopup()"
>
</ava-popup>
