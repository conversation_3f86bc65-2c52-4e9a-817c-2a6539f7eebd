import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FilterConfig } from '../../shared/models/filter.model';
import { PageFooterComponent } from '../../shared/components/page-footer/page-footer.component';
import { PaginationService } from '../../shared/services/pagination.service';
import { AgentServiceService } from './services/agent-service.service';
import agentsConfigData from './constants/agents.json';
import {
  AvaTextboxComponent,
  DropdownComponent,
  DropdownOption,
  IconComponent,
  TextCardComponent,
  PopupComponent,
  ButtonComponent,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';

@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    LucideAngularModule,
    IconComponent,
    ReactiveFormsModule,
    PopupComponent,
    ButtonComponent,
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.scss',
})
export class AgentsComponent implements OnInit {
  allAgents: any[] = [];
  filteredAgents: any[] = [];
  displayedAgents: any[] = [];

  // Separate data arrays for each filter type
  allAgentsData: any[] = [];
  individualAgentsData: any[] = [];
  collaborativeAgentsData: any[] = [];

  // Original data arrays (for search filtering)
  originalAllAgentsData: any[] = [];
  originalIndividualAgentsData: any[] = [];
  originalCollaborativeAgentsData: any[] = [];

  agentsConfig = agentsConfigData;
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;
  totalPages: number = 1;

  // Server-side pagination properties for collaborative agents
  collaborativeCurrentPage: number = 1;
  collaborativeRecordsPerPage: number = 11;
  collaborativeTotalRecords: number = 25; // Constant as requested by user
  collaborativeTotalPages: number = 1;
  agentsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Owned by me', value: 'owned' },
    { name: 'Experience', value: 'experience' },
    { name: 'Product', value: 'product' },
    { name: 'Data', value: 'data' },
    { name: 'Finops', value: 'finops' },
    { name: 'Quality Engineering', value: 'quality' },
    { name: 'Platform', value: 'platform' },
  ];
  selectedData: any = null;
  iconList = [
    { name: 'square-pen', iconName: 'square-pen', cursor: true }, // Edit icon
    { name: 'archive', iconName: 'archive', cursor: true },
    { name: 'trash', iconName: 'trash-2', cursor: true },
    { name: 'copy', iconName: 'copy', cursor: true },
    { name: 'play', iconName: 'play', cursor: true },
  ];
  searchForm!: FormGroup;
  search: any;
  agentFilterConfig!: FilterConfig;
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private agentService: AgentServiceService,
    private fb: FormBuilder,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.fetchAgents();
  }

  fetchAgents(): void {
    this.isLoading = true;
    this.error = null;

    if (this.selectedFilter === 'all') {
      this.fetchAllAgents();
    } else if (this.selectedFilter === 'individual') {
      this.fetchIndividualAgents();
    } else if (this.selectedFilter === 'collaborative') {
      this.fetchCollaborativeAgents();
    }
  }

  fetchAllAgents(): void {
    this.agentService.getAllAgentList().subscribe({
      next: (response) => {
        const agentsList = response.agentWithUseCaseDetails || [];
        const mappedAgents = agentsList.map((agent: any) => {
          const createdAtDate = new Date(agent.createdAt);
          const formattedDate = `${createdAtDate.getMonth() + 1}/${createdAtDate.getDate()}/${createdAtDate.getFullYear()}`;

          return {
            ...agent,
            id: agent.id.toString(),
            title: agent.name,
            name: agent.name,
            description: agent.description || 'No description',
            createdDate: formattedDate,
            userCount: agent.users || 0,
            type: agent.type || 'individual',
          };
        });

        this.originalAllAgentsData = [...mappedAgents];
        this.allAgentsData = [...mappedAgents];
        this.filteredAgents = [...mappedAgents];
        this.updateDisplayedAgents();
        this.isLoading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load agents';
        this.isLoading = false;
      },
    });
  }

  fetchIndividualAgents(): void {
    this.agentService.getAllIndividualAgents().subscribe({
      next: (response) => {
        const agentsList = response.individualAgents || response || [];
        const mappedAgents = agentsList.map((agent: any) => {
          const createdAtDate = new Date(agent.created_at || agent.createdAt);
          const formattedDate = `${createdAtDate.getMonth() + 1}/${createdAtDate.getDate()}/${createdAtDate.getFullYear()}`;

          return {
            ...agent,
            id: agent.useCaseId
              ? agent.useCaseId.toString()
              : agent.id?.toString() || '0',
            title: agent.useCaseName || agent.name || 'Unnamed Agent',
            name: agent.useCaseName || agent.name || 'Unnamed Agent',
            description:
              agent.useCaseDetails || agent.description || 'No description',
            createdDate: formattedDate,
            userCount: agent.users || 0,
            type: 'individual',
          };
        });

        this.originalIndividualAgentsData = [...mappedAgents];
        this.individualAgentsData = [...mappedAgents];
        this.filteredAgents = [...mappedAgents];
        this.updateDisplayedAgents();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching individual agents:', error);
        this.error = error.message || 'Failed to load individual agents';
        this.isLoading = false;
      },
    });
  }

  fetchCollaborativeAgents(): void {
    this.agentService
      .getCollaborativeAgentsPaginated(
        this.collaborativeCurrentPage,
        this.collaborativeRecordsPerPage,
      )
      .subscribe({
        next: (response) => {
          let agentsList: any[] = [];

          // Extract totalNoOfRecords if present
          if (response && typeof response.totalNoOfRecords === 'number') {
            this.collaborativeTotalRecords = response.totalNoOfRecords;
          }

          // Handle the new API response format
          if (Array.isArray(response)) {
            agentsList = response;
          } else if (response && Array.isArray(response.agentDetails)) {
            agentsList = response.agentDetails;
          } else if (
            response &&
            response.data &&
            Array.isArray(response.data)
          ) {
            agentsList = response.data;
          } else if (
            response &&
            response.agents &&
            Array.isArray(response.agents)
          ) {
            agentsList = response.agents;
          } else if (response && response.collaborativeAgents) {
            agentsList = response.collaborativeAgents;
          } else {
            const keys = Object.keys(response || {});
            for (const key of keys) {
              if (Array.isArray(response[key])) {
                agentsList = response[key];
                break;
              }
            }
          }

          const mappedAgents = agentsList.map((agent: any, index: number) => {
            const createdAtDate = new Date(
              agent.modifiedAt || agent.createdAt || new Date(),
            );
            const formattedDate = `${createdAtDate.getMonth() + 1}/${createdAtDate.getDate()}/${createdAtDate.getFullYear()}`;

            const mappedAgent = {
              ...agent,
              id: agent.id ? agent.id.toString() : `collab-${index}`,
              title: agent.name || agent.agentDetails || 'Unnamed Agent',
              name: agent.name || agent.agentDetails || 'Unnamed Agent',
              description:
                agent.description ||
                agent.agentDetails ||
                agent.goal ||
                'No description',
              createdDate: formattedDate,
              userCount: agent.users || 0,
              type: 'collaborative',
              createdBy: agent.createdBy || '',
              role: agent.role || '',
              backstory: agent.backstory || '',
              expectedOutput: agent.expectedOutput || '',
            };

            return mappedAgent;
          });

          // Calculate total pages based on constant total records
          this.collaborativeTotalPages = 2;

          // For collaborative agents, we use server-side pagination
          // so we don't store all data locally, just the current page
          this.collaborativeAgentsData = [...mappedAgents];
          this.filteredAgents = [...mappedAgents];

          // For collaborative agents, we don't use client-side pagination
          this.displayedAgents = [...mappedAgents];
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching collaborative agents:', error);
          this.error = error.message || 'Failed to load collaborative agents';
          this.isLoading = false;
        },
      });
  }

  searchList() {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.filterWorkflow(searchText);
      });
  }

  filterWorkflow(searchText: string): void {
    // Apply search filter to the appropriate data array based on selected filter
    const filterFunction = (agent: any) => {
      const inTitle = agent.title?.toLowerCase().includes(searchText);
      const inDescription = agent.description
        ?.toLowerCase()
        .includes(searchText);
      const inTags =
        Array.isArray(agent.tags) &&
        agent.tags?.some((tag: any) =>
          tag.label?.toLowerCase().includes(searchText),
        );
      return inTitle || inDescription || inTags;
    };

    if (this.selectedFilter === 'all') {
      this.allAgentsData = this.originalAllAgentsData.filter(filterFunction);
      this.filteredAgents = [...this.allAgentsData];
      // Reset to first page when filtering and update pagination
      this.currentPage = 1;
      this.updateDisplayedAgents();
    } else if (this.selectedFilter === 'individual') {
      this.individualAgentsData =
        this.originalIndividualAgentsData.filter(filterFunction);
      this.filteredAgents = [...this.individualAgentsData];
      // Reset to first page when filtering and update pagination
      this.currentPage = 1;
      this.updateDisplayedAgents();
    } else if (this.selectedFilter === 'collaborative') {
      // For collaborative agents with server-side pagination,
      // we would need to implement search on the server side
      // For now, we'll filter the current page data
      this.collaborativeAgentsData =
        this.collaborativeAgentsData.filter(filterFunction);
      this.filteredAgents = [...this.collaborativeAgentsData];
      this.displayedAgents = [...this.collaborativeAgentsData];

      // Note: In a full implementation, you would want to pass the search term
      // to the API and let the server handle the filtering
      this.collaborativeCurrentPage = 1;
    }
  }

  updateDisplayedAgents(): void {
    const paginationResult = this.paginationService.getPaginatedItems(
      this.filteredAgents,
      this.currentPage,
      this.itemsPerPage,
    );
    this.displayedAgents = paginationResult.displayedItems;
    this.totalPages = paginationResult.totalPages;
  }

  onCreateAgent(): void {
    this.router.navigate([agentsConfigData.navigation.createRoute]);
  }

  navigateToBuildAgent(): void {
    this.router.navigate(['/build/agents/build']);
  }

  getHeaderIcons(agent: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'bot', title: agent.toolType || 'Agent' },
      { iconName: 'users', title: `${agent.userCount || 120}` },
    ];
  }

  getFooterIcons(agent: any): { iconName: string; title: string }[] {
    return [
      { iconName: 'user', title: agent.owner || 'AAVA' },
      { iconName: 'calendar-days', title: agent.createdDate },
    ];
  }

  executeAgent(agentId: string): void {
    const filterMap: Record<string, any[]> = {
      all: this.allAgentsData,
      individual: this.individualAgentsData,
      collaborative: this.collaborativeAgentsData,
    };
    const agent =
      filterMap[this.selectedFilter]?.find((a) => a.id === agentId) ||
      this.allAgents.find((a) => a.id === agentId);
    const isCollaborative =
      agent.type === 'collaborative' ||
      this.selectedFilter === 'collaborative' ||
      agent.tags?.some(
        (tag: { label: string }) => tag.label.toLowerCase() === 'collaborative',
      ) ||
      agent.userType === 'collaborative' ||
      agent.agentType === 'collaborative' ||
      Boolean(agent.role || agent.goal || agent.backstory || agent.tools);
    const route = isCollaborative
      ? agentsConfigData.navigation.collaborativeRoute
      : agentsConfigData.navigation.individualRoute;

    this.router.navigate([route], {
      queryParams: {
        id: agentId,
        mode: 'execute',
      },
    });
  }

  onIconClicked(icon: any, agentId: string): void {
    switch (icon.name) {
      case 'trash':
        this.confirmDeleteAgent(agentId);
        break;
      case 'square-pen':
        this.editAgent(agentId);
        break;
      case 'copy':
        this.duplicateAgent(agentId);
        break;
      case 'play':
        this.executeAgent(agentId);
        break;
      default:
        break;
    }
  }

  // Handle edit icon click - navigate to edit mode
  editAgent(agentId: string): void {
    // Find the agent in the appropriate data array based on current filter
    let agent: any = null;

    if (this.selectedFilter === 'all') {
      agent = this.allAgentsData.find((a) => a.id === agentId);
    } else if (this.selectedFilter === 'individual') {
      agent = this.individualAgentsData.find((a) => a.id === agentId);
    } else if (this.selectedFilter === 'collaborative') {
      agent = this.collaborativeAgentsData.find((a) => a.id === agentId);
    }

    // Fallback to legacy arrays if not found
    if (!agent) {
      agent = this.allAgents.find((a) => a.id === agentId);
    }

    if (agent) {
      console.log('Editing agent:', agent);

      // Determine agent type
      let agentType = agent.type || 'individual';

      // If type is not set, determine from filter or agent properties
      if (!agentType || agentType === 'individual') {
        if (
          this.selectedFilter === 'collaborative' ||
          agent.role ||
          agent.goal ||
          agent.backstory ||
          agent.tools
        ) {
          agentType = 'collaborative';
        } else {
          agentType = 'individual';
        }
      }

      console.log(`Editing ${agentType} agent with ID: ${agentId}`);

      // Navigate to build screen in edit mode
      this.router.navigate(['/build/agents', agentType], {
        queryParams: {
          id: agentId,
          mode: 'edit',
        },
      });
    } else {
      console.error(`Agent with ID ${agentId} not found for editing`);
    }
  }

  // Properties for delete confirmation popup
  showDeletePopup: boolean = false;
  agentToDelete: any = null;

  // Properties for success popup
  showSuccessPopup: boolean = false;
  successPopupTitle: string = '';
  successPopupMessage: string = '';

  // Filter properties
  selectedFilter: 'all' | 'individual' | 'collaborative' = 'individual';

  deleteAgent(agentId: string): void {
    // Find the agent in the appropriate data array based on current filter
    let agentObj: any = null;

    if (this.selectedFilter === 'all') {
      agentObj = this.allAgentsData.find((item) => item.id === agentId);
    } else if (this.selectedFilter === 'individual') {
      agentObj = this.individualAgentsData.find((item) => item.id === agentId);
    } else if (this.selectedFilter === 'collaborative') {
      agentObj = this.collaborativeAgentsData.find(
        (item) => item.id === agentId,
      );
    }

    const agentName = agentObj?.name;
    const agentType = agentObj?.type;

    if (agentObj) {
      console.log(`Deleting ${agentType} agent: ${agentName} (ID: ${agentId})`);

      // Determine which delete API to call based on agent type
      let deleteApiCall;

      if (agentType === 'collaborative') {
        // For collaborative agents, use the collaborative delete API
        deleteApiCall = this.agentService.deleteCollaborativeAgent(agentId);
      } else {
        // For individual agents, use the individual delete API
        deleteApiCall = this.agentService.deleteAgent(agentId);
      }

      deleteApiCall.subscribe({
        next: (response: any) => {
          console.log(`Agent "${agentName}" deleted successfully`, response);

          // Remove the agent from the appropriate data arrays
          if (this.selectedFilter === 'all') {
            this.allAgentsData = this.allAgentsData.filter(
              (agent) => agent.id !== agentId,
            );
            this.originalAllAgentsData = this.originalAllAgentsData.filter(
              (agent) => agent.id !== agentId,
            );
          } else if (this.selectedFilter === 'individual') {
            this.individualAgentsData = this.individualAgentsData.filter(
              (agent) => agent.id !== agentId,
            );
            this.originalIndividualAgentsData =
              this.originalIndividualAgentsData.filter(
                (agent) => agent.id !== agentId,
              );
          } else if (this.selectedFilter === 'collaborative') {
            this.collaborativeAgentsData = this.collaborativeAgentsData.filter(
              (agent) => agent.id !== agentId,
            );
            this.originalCollaborativeAgentsData =
              this.originalCollaborativeAgentsData.filter(
                (agent) => agent.id !== agentId,
              );
          }

          // Also remove from legacy arrays if they exist
          this.allAgents = this.allAgents.filter(
            (agent) => agent.id !== agentId,
          );
          this.filteredAgents = this.filteredAgents.filter(
            (agent) => agent.id !== agentId,
          );

          // Update displayed agents after deletion
          this.updateDisplayedAgents();

          // Show success popup with API response message
          this.successPopupTitle = 'Success';
          this.successPopupMessage = response?.message || `Agent "${agentName}" deleted successfully`;
          this.showSuccessPopup = true;
        },
        error: (error: any) => {
          console.error('Error deleting agent:', error);
          console.error(
            `Failed to delete agent "${agentName}". Please try again.`,
          );

          // Show error message to user (you can implement a toast notification here)
          alert(`Failed to delete agent "${agentName}". Please try again.`);
        },
      });
    } else {
      console.error(
        `Agent with ID ${agentId} not found in ${this.selectedFilter} agents.`,
      );
    }
  }

  confirmDeleteAgent(agentId: string): void {
    // Find the agent in the appropriate data array based on current filter
    let agentObj: any = null;

    if (this.selectedFilter === 'all') {
      agentObj = this.allAgentsData.find((item) => item.id === agentId);
    } else if (this.selectedFilter === 'individual') {
      agentObj = this.individualAgentsData.find((item) => item.id === agentId);
    } else if (this.selectedFilter === 'collaborative') {
      agentObj = this.collaborativeAgentsData.find(
        (item) => item.id === agentId,
      );
    }

    // Fallback to legacy arrays if not found
    if (!agentObj) {
      agentObj = this.allAgents.find((item) => item.id === agentId);
    }

    console.log('Confirming delete for agent:', agentObj);
    this.agentToDelete = agentObj;
    this.showDeletePopup = true;
  }

  duplicateAgent(agentId: string): void {
    console.log('Duplicating agent with ID:', agentId);

    // Find the agent in the appropriate array based on current filter
    let agent = null;
    let agentType = 'individual'; // default

    if (this.selectedFilter === 'all') {
      // Search in all agents
      agent = this.allAgentsData.find((a) => a.id === agentId);
      if (agent) {
        // Determine agent type from agent properties
        if (agent.role || agent.goal || agent.backstory || agent.tools) {
          agentType = 'collaborative';
        } else {
          agentType = 'individual';
        }
      }
    } else if (this.selectedFilter === 'individual') {
      agent = this.individualAgentsData.find((a) => a.id === agentId);
      agentType = 'individual';
    } else if (this.selectedFilter === 'collaborative') {
      agent = this.collaborativeAgentsData.find((a) => a.id === agentId);
      agentType = 'collaborative';
    }

    if (agent) {
      console.log('Duplicating agent:', agent);
      console.log(`Duplicating ${agentType} agent with ID: ${agentId}`);

      // Navigate to build screen in duplicate mode
      this.router.navigate(['/build/agents', agentType], {
        queryParams: {
          id: agentId,
          mode: 'duplicate',
        },
      });
    } else {
      console.error(`Agent with ID ${agentId} not found for duplication`);
    }
  }

  // Handle delete confirmation popup
  onConfirmDelete(): void {
    if (this.agentToDelete) {
      this.deleteAgent(this.agentToDelete.id);
      this.closeDeletePopup();
    }
  }

  closeDeletePopup(): void {
    this.showDeletePopup = false;
    this.agentToDelete = null;
  }

  // Handle success popup
  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.successPopupTitle = '';
    this.successPopupMessage = '';
  }

  onSuccessConfirm(): void {
    this.closeSuccessPopup();
  }

  // Handle filter button clicks
  onFilterChange(filter: 'all' | 'individual' | 'collaborative'): void {
    this.selectedFilter = filter;
    this.currentPage = 1; // Reset to first page when changing filter
    this.collaborativeCurrentPage = 1; // Reset collaborative pagination too
    this.fetchAgents(); // Fetch agents based on new filter
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    if (this.selectedFilter === 'collaborative') {
      // Server-side pagination for collaborative agents
      this.collaborativeCurrentPage = page;
      this.fetchCollaborativeAgents();
    } else {
      // Client-side pagination for individual agents
      this.currentPage = page;
      this.updateDisplayedAgents();
    }
  }

  onCollaborativePageChange(page: number): void {
    this.collaborativeCurrentPage = page;
    this.fetchCollaborativeAgents();
  }

  get showCreateCard(): boolean {
    if (this.selectedFilter === 'collaborative') {
      return (
        this.collaborativeCurrentPage === 1 && !this.isLoading && !this.error
      );
    }
    return this.currentPage === 1 && !this.isLoading && !this.error;
  }

  getFilterDisplayName(): string {
    switch (this.selectedFilter) {
      case 'all':
        return 'Agents';
      case 'individual':
        return 'Individual Agents';
      case 'collaborative':
        return 'Collaborative Agents';
      default:
        return 'Agents';
    }
  }
}
