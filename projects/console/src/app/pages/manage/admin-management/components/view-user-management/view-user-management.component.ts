import { Component, inject, signal } from '@angular/core';
import { UserManagementService } from '../../services/user-management.service';
import { ButtonComponent, PopupComponent } from '@ava/play-comp-library';
import { Router } from '@angular/router';
import { TableGridComponent } from 'projects/console/src/app/shared/components/table-grid/table-grid.component';
import { columnDefs } from 'projects/console/src/app/shared/components/table-grid/model/table-grid.model';
import { AdminManagementCellComponent } from './renderer/action-management-renderer.component';
import { DrawerService } from 'projects/console/src/app/shared/services/drawer/drawer.service';
import { UserManagementPreviewPanelComponent } from './renderer/user-management-preview-panel.component';

@Component({
  selector: 'app-view-user-management',
  imports: [ButtonComponent, TableGridComponent, PopupComponent],
  templateUrl: './view-user-management.component.html',
  styleUrl: './view-user-management.component.scss'
})
export class ViewUserManagementComponent {
  readonly columns: columnDefs[] = [
    {
      header: 'Name',
      field: 'userName',
      cellRenderer: AdminManagementCellComponent,
      cellRendererParams: { context: { componentParent: this } },
    },
    { header: 'Email', field: 'email' },
    { header: 'Access', field: 'roles' },
    { header: 'Added On', field: 'createdAt' },
    { header: 'Authorized By', field: 'authorizedBy' },
    {
      header: 'Action',
      field: 'action',
      cellRenderer: AdminManagementCellComponent,
      maxWidth: 120,
      cellRendererParams: { context: { componentParent: this } },
    },
  ];
  rows = signal<any[]>([]);
  totalRecords = signal<number | undefined>(undefined);
  currentPage = signal<number>(1);
  itemsPerPage = signal<number>(10);
  showUserDeletePopup = signal<boolean>(false);
  showDeleteStatusPopup = signal<boolean>(false);
  userId = signal<string>('');

 private readonly userManagementService = inject(UserManagementService);
 private readonly router = inject(Router);
 private readonly drawerService = inject(DrawerService);

  ngOnInit(): void {
    this.getUserList();
  }

  getUserList() {
    this.userManagementService.getAllUsers(this.currentPage(), this.itemsPerPage()).subscribe({
      next: (res: any) => {
        const { userMgmtResponse, totalNoOfRecords } = res;
        this.totalRecords.set(totalNoOfRecords);
        userMgmtResponse.forEach((user: any) => {
          user.realms = user.realms.split(',').map((realms: any) => realms.trim())
        });
        this.rows.set(userMgmtResponse || []);
      },
      error: (e) => console.error('Failed to load users', e)
    })
  }

  addNewUser() {
    this.router.navigate(['manage/admin-management/add-user']);
  }

  openPreviewPanel(data: any) {
    this.drawerService.open(UserManagementPreviewPanelComponent, {
      metaData: data,
      onParentAction: (id: string) => this.deleteUser(id), 
      closePreview: (() => this.drawerService.clear()) as any,
    } as any);
  }

  deleteUser(value: string) {
    this.userId.set(value);
    this.showUserDeletePopup.set(true);
  }

  onPageChange(page: number) {
    this.currentPage.set(page);
    this.getUserList();
  }

  onConfirmUserDelete() {
    this.closeUserDeletePopup();
    this.userManagementService.removeUser(this.userId()).subscribe({
      next: () => {
        this.getUserList();
        this.showDeleteStatusPopup.set(true);
      },
      error: e => console.error(e)
    })
  }

  closeUserDeletePopup() {
    this.showUserDeletePopup.set(false);
  }

  closeSuccessPopup() {
    this.showDeleteStatusPopup.set(false);    
  }
}
