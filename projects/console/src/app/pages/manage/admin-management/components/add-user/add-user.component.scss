.basic__info--section {
    display: flex;
    gap: 24px;
}

.input {
    width: 412px;
}

.dropdown {
    width: 336px;
}

.access__control--container {
    margin-top: 32px;
}

.access__control--section {
    display: flex;
    border-bottom: 1px solid #BBBEC5;
    gap: 16px;
    align-items: center;
    padding-bottom: 20px;

    .title {
        font-size: 18px;
        font-weight: 500;
        font-family: Mulish;
        width: 180px;
        color: #000000;
    }
}

.tag--container {
    display: flex;
    gap: 10px;
}

.dropdown-tag {
    padding: 0 0 0 40px;
}

.realm-container {
    margin-top: 46px;
}

.realm-section {
    height: 218px;
    border: 1px solid #BBBEC5;
    border-radius: 1rem;
    padding: 1rem;
    margin: 24px 0 0 0;

    .realm__filter--section {
        display: flex;
        gap: 10px;

        ava-autocomplete {
            flex: 1;
        }
    }

    .realm {
        margin: 24px 0 0 0 ;
    }
}

.title-sm {
    font-size: 14px;
    color: #898E99;
    font-weight: 600;
}

.realm__form {
    display: flex;
    flex-flow: column;
    gap: 16px;    
}

.form-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.input__field--wrapper {
    flex: 1;
    width: 360px;
}

.filter-label {
  display: block;
  font-weight: 500;
  text-align: left;
  color: #14161F;
}

.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.button__container {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin: 16px 0 16px 0;

    &.main {
        justify-content: flex-end;
    }
}

.role__form-fields {
    display: flex;
    flex-flow: column;
    gap: 16px;
}