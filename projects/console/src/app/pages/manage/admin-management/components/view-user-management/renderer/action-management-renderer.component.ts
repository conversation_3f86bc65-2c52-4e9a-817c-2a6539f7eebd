import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { IconComponent } from '@ava/play-comp-library';
import { CellRenderer, CellRendererParams } from 'projects/console/src/app/shared/components/table-grid/model/table-grid.model';

@Component({
  selector: 'app-admin-management-cell',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    @if (params.colDef.header === 'Name') {
      <div class="name-container" (click)="onCellClick()">{{ params.value }}</div>
    } @else {
      <ava-icon
        class="delete"
        (click)="deleteUser()"
        [iconSize]="16"
        iconName="trash-2"
        iconColor="#1C1B1F"
      ></ava-icon>
    }
  `,
  styles: [
    `
      .name-container {
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    `,
  ],
})
export class AdminManagementCellComponent implements CellRenderer {
  params!: any;

  aweInit(params: CellRendererParams) {
    this.params = params;
  }

  deleteUser() {
    this.params.context.componentParent.deleteUser(this.params.rowData.userId);
    console.log(this.params.rowData.userId)
  }

  onCellClick() {
    this.params.context.componentParent.openPreviewPanel(this.params.rowData);
  }
}
