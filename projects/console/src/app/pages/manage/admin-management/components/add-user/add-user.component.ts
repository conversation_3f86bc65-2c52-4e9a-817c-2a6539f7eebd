import { Component, inject, signal } from '@angular/core';
import { UserManagementService } from '../../services/user-management.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AvaAutocompleteComponent, AvaOptionComponent, AvaTagComponent, AvaTextboxComponent, ButtonComponent, DropdownComponent, DropdownOption, PopupComponent } from '@ava/play-comp-library';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DropdownActionOption, Page } from '../../models/user-management.model';
import { OrgConfigService } from '../../../../org-config/services/org-config.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
@Component({
  selector: 'app-add-user',
  imports: [
    ReactiveFormsModule,
    AvaTextboxComponent, 
    DropdownComponent,
    AvaO<PERSON>Component,
    Ava<PERSON><PERSON><PERSON>omponent,
    AvaAutocompleteComponent,
    ButtonComponent,
    PopupComponent 
  ],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss'
})
export class AddUserComponent {
  userManagementForm!: FormGroup;
  addRealmForm!: FormGroup;
  addRoleForm!: FormGroup;
  roleList = signal<DropdownOption[]>([]);
  pageList = signal<Page[]>([]);
  actionList = signal<DropdownOption[]>([]);
  actionCheckboxList = signal<string[]>([]);
  realmsList = signal<any[]>([]);
  selectedTags: { [key: string]: DropdownActionOption[] } = {};
  selectedRealmTag: any[] = [];
  selectedValues: { [key: string]: string[] } = {};
  selectedRealmsTag: any[] = [];
  showRealmPopup = signal<boolean>(false);
  showCreateRolePopup = signal<boolean>(false);
  hierarchyData = signal<any[]>([]);
  showStatusPopup = signal<boolean>(false);
  isAccessControl = signal<boolean>(false);
  accessControlList = signal<any[]>([]);
  selectedTagRemovable = signal<boolean>(true);
  selectedRoleValue: string = '';
  userId!: number;
  showUserSuccessPopup: boolean = false;
  userSuccessMessage: string = ''; 
  statusSuccessMessage: string = ''; 

  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];

  selectedOrgName: string = '';
  selectedDomainName: string = '';
  selectedProjectName: string = '';
  selectedTeamName: string = '';

  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  private userManagementService = inject(UserManagementService);
  private orgConfigService = inject(OrgConfigService);
  private router = inject(Router);
  private formBuilder = inject(FormBuilder);
  private tokenStorage = inject(TokenStorageService);
  private activatedRoute = inject(ActivatedRoute);

 
  ngOnInit(): void {
    this.getRoleList();
    this.getPageList();
    this.getActionList();
    this.getRealmsList();
    this.getOrgList();
    this.userManagementForm = this.getUserManagementForm();
    this.addRealmForm = this.getAddRealmForm();
    this.addRoleForm = this.getAddRoleForm();
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if(params?.id) {
        this.userId = params.id;
        this.getViewUser(params.id);
      }
    });
  }

  getViewUser(id: number) {
    this.userManagementService.getUserDetails(id).subscribe({
      next: (res: any) => {
        this.userManagementForm.patchValue({
          email: res.email,
          role: res.roles[0].roleId
        })
        this.selectedRoleValue = res.roles[0].roleName;
        this.onRoleSelectionChange(res.roles[0].roleId);
        this.selectedRealmTag = res.realms.map((realm: any) => ({label: realm.realmName, value: realm.realmId}));
        
      },
      error: e => console.error(e)
    })
  }

  getUserManagementForm() {
    return this.formBuilder.group({
      email: ['', [Validators.required]],
      role: ['', [Validators.required]],
    })
  }

  getAddRealmForm() {
    return this.formBuilder.group({
      realmName: [null, [Validators.required]],
      orgId: [null, [Validators.required]],
      domainId: [null, [Validators.required]],
      projectId: [null, [Validators.required]],
      teamId: [null, [Validators.required]],
    });
  }

  getAddRoleForm() {
    return this.formBuilder.group({
      roleName: ['', [Validators.required]],
      description: ['', [Validators.required]],
    });
  }

  getRoleList() {
    this.userManagementService.getAllRoles().subscribe({
      next: (userMgmtResponse: any) => {
        this.roleList.set(userMgmtResponse.map((opt: any) => ({
          name: opt.roleName,
          value: opt.roleId,
        })));
      },
      error: (e) => console.error(e)
    })
  }

  getPageList() {
    this.userManagementService.getAllPages().subscribe({
      next: (pages: any) => {
        this.pageList.set(pages);
        this.initializePageControl();
      },
      error: (e) => console.error(e)
    })
  }

  getActionList() {
    this.userManagementService.getAllActions().subscribe({
      next: (actions: any) => {
        this.actionList.set(actions.map((opt: any) => ({
          name: opt.actionName,
          value: opt.actionId,
        })));
        this.actionCheckboxList.set(actions.map((opt: any) => opt.actionName));
      },
      error: (e) => console.error(e)
    })
  }

  getRealmsList() {
    this.userManagementService.getAllRealms().subscribe({
      next: (realms: any) => {
        this.realmsList.set(realms.map((opt: any) => ({
          label: opt.realmName,
          value: opt.realmId,
        })));
      },
      error: (e) => console.error(e)
    })
  }

  getOrgList() {
    this.orgConfigService.getOrganizationHierarchy().subscribe({
      next: (data: any) => {
        this.hierarchyData.set(data);
        this.loadOrganizations();
      }
    })
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData().map((org) => ({
      name: org.organizationName,
      value: org.orgId.toString(),
    }));
  }

  initializePageControl() {
    this.pageList().forEach((page: any) => {
      this.userManagementForm.addControl(page.pageId, this.formBuilder.control([]));
    })
  }

  getControl(name: string): FormControl {
    return this.userManagementForm.get(name) as FormControl;
  }

  getSelectedValues(pageId: string): any[] {
    const control = this.userManagementForm.get(pageId);
    if (!control || !control.value) {
      return [];
    }    
    return [control.value];
  }

  getFieldError(fieldName: string): string {
    // const field = this.knowledgeBaseForm.get(fieldName);
    // // Capitalize only if first letter is not already uppercase
    // const formattedFieldName = /^[A-Z]/.test(fieldName)
    //   ? fieldName
    //   : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    // if (field && field.invalid && (field.touched || field.dirty)) {
    //   if (field.errors?.['required']) {
    //     return `${formattedFieldName} is required`;
    //   }
    //   if (field.errors?.['email']) {
    //     return 'Please enter a valid email address';
    //   }
    //   if (field.errors?.['minlength']) {
    //     return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;
    //   }
    // } else if(fieldName == 'scheme' || fieldName == 'embeddingModel') {
    //    if (field && field.errors?.['required']) {
    //     return `${formattedFieldName} is required`;
    //   }
    // } 
    return '';
  }

  onSelectionChange(event: any, formControl: string) {
    const selectedValues = event.selectedOptions.map((option: any) => option.value);
    this.userManagementForm.get(formControl)?.setValue(selectedValues)
    this.selectedTags[formControl] = event.selectedOptions;
  }

  addNewRole() {
    this.showCreateRolePopup.set(true);
  }

  onValueChange(event: any) {

  }

  onOptionSelected(event: any) {
    const exists = this.selectedRealmTag.some(item => item.value === event.value);
    if (!exists) {
      this.selectedRealmTag.push(event);
    }
  }

  removeRealmTag(tagValue: number) {
    this.selectedRealmTag = this.selectedRealmTag.filter(tag => tag.value !== tagValue);
  }

  removeTag(event: any, formControl: string) {
    this.selectedTags[formControl] = this.selectedTags[formControl].filter(
      option => option.value !== event.value
    );
    const values = this.selectedTags[formControl].map(option => option.value);
    const DropdownSelectedvalues = this.selectedTags[formControl].map(option => option.name);
    this.userManagementForm.get(formControl)?.setValue(values);
    this.selectedValues[formControl] = DropdownSelectedvalues;
  }

  onRemoveRealm() {

  }

  openRealmPopup() {
    this.showRealmPopup.set(true);
  }

  onOrgSelect(event: any) {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    const selectedOrgName = event.selectedOptions?.[0]?.name;
    if (selectedOrgId) {
      this.selectedOrg = selectedOrgId;
      this.selectedOrgName = selectedOrgName;
      this.addRealmForm.patchValue({ orgId: selectedOrgId, domainId: '', projectId: '', teamId: ''  });
      this.loadDomains(selectedOrgId);
      this.selectedDomain = '';
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedDomainName = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.projectOptions = [];
      this.teamOptions = [];
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData().find((o) => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map((domain: any) => ({
        name: domain.domainName,
        value: domain.domainId.toString(),
      }));
    } else {
      this.domainOptions = [];
    }
  }

   onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    const selectedDomainName = event.selectedOptions?.[0]?.name;
    if (selectedDomainId) {
      this.selectedDomain = selectedDomainId;
      this.selectedDomainName = selectedDomainName;
      this.addRealmForm.patchValue({ domainId: selectedDomainId, projectId: '', teamId: '' });
      this.loadProjects(selectedDomainId);
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.teamOptions = [];
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData().find((o) =>
      o.domains.some((d: any) => d.domainId.toString() === domainId),
    );
    if (org) {
      const domain = org.domains.find(
        (d: any) => d.domainId.toString() === domainId,
      );
      if (domain) {
        this.projectOptions = domain.projects.map((project: any) => ({
          name: project.projectName,
          value: project.projectId.toString(),
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    const selectedProjectName = event.selectedOptions?.[0]?.name;
    if (selectedProjectId) {
      this.selectedProject = selectedProjectId;
      this.selectedProjectName = selectedProjectName;
      this.addRealmForm.patchValue({ projectId: selectedProjectId, team: '' });
      this.loadTeams(selectedProjectId);
      this.selectedTeam = '';
      this.selectedTeamName = '';
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData().find((o) =>
      o.domains.some((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId),
      ),
    );
    if (org) {
      const domain = org.domains.find((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId),
      );
      if (domain) {
        const project = domain.projects.find(
          (p: any) => p.projectId.toString() === projectId,
        );
        if (project) {
          this.teamOptions = project.teams.map((team: any) => ({
            name: team.teamName,
            value: team.teamId.toString(),
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    const selectedTeamName = event.selectedOptions?.[0]?.name;
    if (selectedTeamId) {
      this.selectedTeam = selectedTeamId;
      this.selectedTeamName = selectedTeamName;
      this.addRealmForm.patchValue({ teamId: selectedTeamId });
    }
  }

  createRole() {
    if(this.addRoleForm.valid) {
      const payload = this.addRoleForm.value;
      this.userManagementService.createRole(payload).subscribe({
        next: (res: any) => {
          this.roleList.update(currentList => [...currentList, {name: res.roleName, value: res.roleId}]);
          this.selectedRoleValue = res.roleName;
          this.onRoleSelectionChange(res.roleId);
          this.closeRealmPopup();
          this.addRoleForm.reset();
          this.statusSuccessMessage = 'The Role has been successfully created!';
          this.showStatusPopup.set(true);
        },
        error: e => console.error(e)
      })
    }
  }

  createRealm() {
    if(this.addRealmForm.valid) {
      const {realmName, teamId} = this.addRealmForm.value;
      this.userManagementService.createRealm(realmName, teamId).subscribe({
        next: (res: any) => {
          this.selectedRealmTag.push({label: res.realmName, value: res.realmId});
          this.closeRealmPopup();
          this.statusSuccessMessage = 'The Realm has been successfully created!';
          this.showStatusPopup.set(true);
        },
        error: e => console.error(e)
      })
    }
  }

  closeRealmPopup() {
    this.showRealmPopup.set(false);
    this.showCreateRolePopup.set(false);
  }

  closeSuccessPopup() {
    this.showStatusPopup.set(false);
  }

  onRoleSelectionChange(event: any) {
    const roleId = event?.selectedOptions?.[0]?.value ?? event;
    this.userManagementForm.get('role')?.patchValue(roleId);
    this.userManagementService.getExistingAccessControl(roleId).subscribe({
      next: (res: any) => {
        this.resetAccessControll();
        this.accessControlList.set(res.accessControl);
        if(!res.accessControl?.length) {
          this.isAccessControl.set(true);
          this.selectedTagRemovable.set(true);
          return;
        }
        this.isAccessControl.set(false);
        this.selectedTagRemovable.set(false);
        this.updatedUserManagementFormValues();
      },
      error: e => console.error(e)
    })
  }

  updatedUserManagementFormValues() {
    const actionMap = new Map( this.accessControlList().map(item => [item.page, item.actions]));
    const actionIdsMap = new Map( this.actionList().map(item => [item.name, item.value]) );

    this.pageList().forEach(page => {
      const pageActions = this.accessControlList().find(pa => pa.page === page.pageName);
      if (pageActions) {
        this.selectedValues[page.pageId.toString()] = pageActions.actions;
        this.selectedTags[page.pageId.toString()] = pageActions.actions.map((op: string) => ({ name: op }));
      }

      const formControlName = page.pageId.toString();
      const formControl = this.userManagementForm.get(formControlName);
      const actionNames = actionMap.get(page.pageName);
      if (formControl && actionNames) {
        const actionIds = actionNames
          .map((name: any) => actionIdsMap.get(name))
          .filter((id: any) => id !== undefined);
        formControl.patchValue(actionIds);
      }
    });
  }

  resetAccessControll() {
    this.pageList().forEach(page => {
      const formControlName = page.pageId.toString();
      this.selectedValues[formControlName] = [];
      this.selectedTags[formControlName] = [];
      this.userManagementForm.get(formControlName)?.patchValue([]);
    })
  }

  onExit() {
    this.router.navigate(['manage/admin-management']);
  }

  addNewUser() {
    if(this.userManagementForm.valid && this.selectedRealmTag?.length) {
      const { email, role, ...pagePermissions } = this.userManagementForm.value;
      const roleId = parseInt(role);
      const roleIds = [roleId];
      const payload: any = {
        email: email,
        roleIds: roleIds,
        realmIds: this.selectedRealmTag.map(realm => realm.value),
        authorizedBy: this.tokenStorage.getDaUsername() || '<EMAIL>',
      };

      if(this.selectedTagRemovable()) {
        const permissions: Array<{ pageId: number; actionId: number }> = [];
    
        Object.entries(pagePermissions).forEach(([pageId, actionIds]) => {
          if (Array.isArray(actionIds) && actionIds.length > 0) {
            actionIds.forEach((actionId: number) => {
              permissions.push({
                pageId: parseInt(pageId),
                actionId: actionId
              });
            });
          }
        });
        
        payload.rolePermessionRequest = {
          roleId: roleId,
          permissions: permissions
        };
      }

      if(this.userId) {
        this.userManagementService.updateUser(payload, this.userId).subscribe({
          next: (res: any) => {
            this.userSuccessMessage = 'User update successfully';
            this.showUserSuccessPopup = true;
          },
          error: e => console.error(e)
        })
      } else {
        this.userManagementService.addNewUser(payload).subscribe({
          next: (res: any) => {
            this.userSuccessMessage = 'User created successfully';
            this.showUserSuccessPopup = true;
          },
          error: e => console.error(e)
        })
      }
    }
  }

  onUserSuccessConfirm() {
    this.showUserSuccessPopup = false;
    this.router.navigate(['manage/admin-management']);    
  }
}











