<div class="user__management--wrapper">
    <!-- <h2>User Management</h2> -->

    <div class="user__management--container">
        <div class="filter--section">
            <div class="filter"></div>
            <div>
                <ava-button
                  label="Add User"
                  variant="primary"
                  size="medium"
                  (userClick)="addNewUser()"
                >
                </ava-button>
            </div>
        </div>
        <div class="user__management--table">     
            <awe-table-grid 
                [columnDefs]="columns" 
                [serverPagination]="true" 
                [rowData]="rows()" 
                [height]="510" 
                [pagination]="true"
                [totalItems]="totalRecords()" 
                (pageChange)="onPageChange($event)"
            ></awe-table-grid>
        </div>
    </div>
</div>

<!-- Delete Confirmation Popup -->
<ava-popup
  [show]="showUserDeletePopup()"
  title=""
  [message]="'Are you sure you want to delete ?'"
  [showHeaderIcon]="true"
  headerIconName="trash"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Delete'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="onConfirmUserDelete()"
  (cancel)="closeUserDeletePopup()"
  (closed)="closeUserDeletePopup()"
>
</ava-popup>

<ava-popup
    [show]="showDeleteStatusPopup()"
    [title]="'Success'"
    [message]="'user deleted successfully'"
    [showHeaderIcon]="true"
    headerIconName="check-circle"
    iconColor="#28a745"
    [showClose]="true"
    [showCancel]="false"    
    (closed)="closeSuccessPopup()"
>
</ava-popup>