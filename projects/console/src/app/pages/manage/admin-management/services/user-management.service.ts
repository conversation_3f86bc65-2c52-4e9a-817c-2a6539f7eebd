import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UserManagementService {
  private apiAuthUrl = environment.consoleApiAuthUrl;

  private http = inject(HttpClient); 

  getAllUsers(page: number, records: number) {
    const url = `${this.apiAuthUrl}/user/mgmt?page=${page}&records=${records}`;
    return this.http.get(url);
  }

  getUserDetails(id: number) {
    const url = `${this.apiAuthUrl}/user?userId=${id}`;
    return this.http.get(url);
  }

  getAllRoles() {
    const url = `${this.apiAuthUrl}/roles`;
    return this.http.get(url);
  }

  getAllPages() {
    const url = `${this.apiAuthUrl}/pages`;
    return this.http.get(url);
  }

  getAllActions() {
    const url = `${this.apiAuthUrl}/actions`;
    return this.http.get(url);
  } 

  getAllRealms() {
    const url = `${this.apiAuthUrl}/realms`;
    return this.http.get(url);
  }
  
  getExistingAccessControl(roleId: string) {
    const url = `${this.apiAuthUrl}/access/permissions?roleId=${roleId}`;
    return this.http.get(url);
  }

  addNewUser(payload: any) {
    const url = `${this.apiAuthUrl}/user`;
    return this.http.post(url, payload);
  }

  updateUser(payload: any, userId: number) {
    const url = `${this.apiAuthUrl}/user?userId=${userId}`;
    return this.http.post(url, payload);
  }

  createRealm(name: string, teamId: string) {
    const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}`;
    return this.http.post(url, null);
  }

  createRole(payload: any) {
    const url = `${this.apiAuthUrl}/role`;
    return this.http.post(url, payload);
  }

  removeUser(id: string) {
    const url = `${this.apiAuthUrl}/user/${id}`;
    return this.http.delete(url);
  }
}
