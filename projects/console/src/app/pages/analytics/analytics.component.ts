import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import * as Highcharts from 'highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import NoDataToDisplay from 'highcharts/modules/no-data-to-display';
import HC_more from 'highcharts/highcharts-more';
import HC_Exporting from 'highcharts/modules/exporting';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { TabItem, ButtonComponent, CalendarComponent, TextCardComponent, IconComponent } from '@ava/play-comp-library';
import { AnalyticsService } from '../../shared/services/analytics.service';
import { environment } from 'projects/console/src/environments/environment';

HC_more(Highcharts);
NoDataToDisplay(Highcharts);
HC_Exporting(Highcharts);

// Remove AvaTab interface since we'll use TabItem from the library

interface DateRange {
  fromDate: string;
  toDate: string;
}

interface ChartColors {
  primary: string;
  secondary: string;
  tertiary: string;
  success: string;
  warning: string;
  danger: string;
}

interface ChartDefaults {
  backgroundColor: string;
  fontFamily: string;
  height: number;
}
@Component({
  selector: 'app-analytics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HighchartsChartModule,
    MatSelectModule,
    MatFormFieldModule,
    MatOptionModule,
    MatInputModule,
    ButtonComponent,
    CalendarComponent,
    TextCardComponent,
    IconComponent
  ],
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss']
})
export class AnalyticsComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly CHART_COLORS: ChartColors = {
    primary: '#4F46E5',      // Modern Indigo
    secondary: '#059669',    // Emerald Green
    tertiary: '#DC2626',     // Modern Red
    success: '#10B981',      // Success Green
    warning: '#F59E0B',      // Amber Warning
    danger: '#EF4444'        // Error Red
  };

  // Professional color palettes for different chart types
  private readonly PROFESSIONAL_COLORS = {
    userConsumption: '#8B5CF6',     // Purple - User related
    linesOfCode: '#3B82F6',         // Blue - Code related
    topUseCases: '#10B981',         // Green - Success/Usage
    numberOfRequests: '#F59E0B',    // Orange - Activity
    topLanguages: '#EF4444',        // Red - Languages
    userResponse: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981'], // Multi-color for pie
    userActivity: ['#10B981', '#EF4444'], // Green/Red for active/inactive
    responseTime: '#06B6D4',        // Cyan - Performance
    studioUsage: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#EF4444'], // Multi-color
    topAgents: '#EC4899',           // Pink - Agents
    agentCreated: '#14B8A6',        // Teal - Creation
    toolAnalytics: ['#F59E0B', '#8B5CF6'], // Orange/Purple for tools
    toolUsage: '#F97316',           // Orange - Tool usage
    adoptionRate: '#8B5CF6',        // Purple - Adoption
    collaborativeUserConsumption: '#EC4899' // Pink - Collaborative users
  };
  private readonly CHART_DEFAULTS: ChartDefaults = {
    backgroundColor: 'transparent',
    fontFamily: 'Mulish, sans-serif',
    height: 320
  };
  Highcharts: typeof Highcharts = Highcharts;
  vmFG!: FormGroup;
  dateRange: DateRange = {
    fromDate: this.getDefaultFromDate(),
    toDate: this.getDefaultToDate()
  };
  activeTab: string = 'usecase';

  tabs: TabItem[] = [
    {
      id: 'usecase',
      label: 'Individual'
    },
    {
      id: 'agents',
      label: 'Collaborative'
    }
  ];

  tabListStyle = {
    'background': 'var(--card-bg)',
    'border': '1px solid var(--card-border)',
    'border-radius': '8px',
    'padding': '0.25rem'
  };
  isLoading = false;
  usageLoader = false;
  metricesLoader = false;
  consumptionLoader = false;
  agentMetricsLoader = false;
  chartLoadingStates: { [key: string]: boolean } = {};
  chartOptions: { [key: string]: Highcharts.Options } = {};
  chartRefs: { [key: string]: Highcharts.Chart } = {};
  analyticsData: any = {};
  useCaseMetrics: any = {};
  agentMetrics: any = {};
  userActivity: any = {};
  userActivityStats: any = {};
  userConsumption: any[] = [];
  toolUsage: any[] = [];
  sortedAnalytics: any[] = [];
  agentMetricsTableData: any[] = [];
  expandedRows: Set<number> = new Set();
  noDataAvailable = false;
  agentMetricsNoDataAvailable = false;
  selectedUseCases: string[] = [];
  useCaseList: any[] = [];
  sortedUseCaseList: any[] = [];
  searchText = '';
  filteringEnabled = false;
  showDownloadOptions = false;
  public langfuseUrl = environment.consoleLangfuseUrl;
  public ICLAnalyticsUrl = environment.consoleICLAnalyticsUrl;

  constructor(
    private analyticsService: AnalyticsService,
    private fb: FormBuilder
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.initializeAnalytics();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.vmFG = this.fb.group({
      fromDate: [this.dateRange.fromDate],
      toDate: [this.dateRange.toDate]
    });
    this.vmFG.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(values => {
      if (values.fromDate && values.toDate) {
        this.dateRange.fromDate = values.fromDate;
        this.dateRange.toDate = values.toDate;
      }
    });
  }

  private initializeAnalytics(): void {
    this.loadAnalyticsData();
  }

  onTabChange(tabItem: TabItem): void {
    this.activeTab = tabItem.id;
    this.loadTabSpecificData(tabItem.id);
  }

  getActiveTab(): TabItem {
    return this.tabs.find(tab => tab.id === this.activeTab) || this.tabs[0];
  }

  private loadTabSpecificData(tabId: string): void {
    switch (tabId) {
      case 'usecase':
        this.loadUseCaseData();
        break;
      case 'agents':
        this.loadAgentData();
        break;
    }
  }

  onDateRangeChange(): void {
    const formValues = this.vmFG.value;
    if (formValues.fromDate && formValues.toDate) {
      this.dateRange.fromDate = formValues.fromDate;
      this.dateRange.toDate = formValues.toDate;
    }
  }

  applyFilter(): void {
    this.onDateRangeChange();
    if (!this.isValidDateRange()) {
      console.error('Invalid date range selected');
      return;
    }
    this.loadAnalyticsData();
  }

  private isValidDateRange(): boolean {
    const fromDate = new Date(this.dateRange.fromDate);
    const toDate = new Date(this.dateRange.toDate);
    return fromDate <= toDate && fromDate <= new Date();
  }

  private getDefaultFromDate(): string {
    const date = new Date();
    // Set to the 1st day of the current month
    date.setDate(1);
    return date.toISOString().split('T')[0];
  }

  private getDefaultToDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  private formatDateForAPI(dateString: string): string {
    const [year, month, day] = dateString.split('-');
    return `${day}-${month}-${year}`;
  }

  private getFormattedDateRangeForAPI(): DateRange {
    return {
      fromDate: this.formatDateForAPI(this.dateRange.fromDate),
      toDate: this.formatDateForAPI(this.dateRange.toDate)
    };
  }

  private loadAnalyticsData(): void {
    this.isLoading = true;
    switch (this.activeTab) {
      case 'usecase':
        this.loadUseCaseData();
        break;
      case 'agents':
        this.loadAgentData();
        break;
      default:
        this.isLoading = false;
    }
  }

  private loadUseCaseData(): void {
    this.isLoading = true;
    this.usageLoader = true;
    const formattedDateRange = this.getFormattedDateRangeForAPI();
    forkJoin({
      mainAnalytics: this.analyticsService.getAllAnalyticsData(formattedDateRange),
      totalRequestMetrics: this.analyticsService.totalRequestCount(formattedDateRange),
      userConsumption: this.analyticsService.getUserUsageAnalytics(formattedDateRange),
      userActivity: this.analyticsService.getUserActivity(formattedDateRange),
      responseTime: this.analyticsService.getResponseTime(formattedDateRange)
    }).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (responses) => {
          this.parseAndCreateChartsWithSeparateAPIs(responses);
          this.useCaseMetrics = responses.totalRequestMetrics;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading analytics data:', error);
          this.isLoading = false;
        }
      });
  }

  private parseAndCreateChartsWithSeparateAPIs(responses: any): void {
    const mainData = responses.mainAnalytics;
    const userConsumptionData = responses.userConsumption;
    const userActivityData = responses.userActivity;
    const responseTimeData = responses.responseTime;

    Object.keys(this.chartLoadingStates).forEach(key => {
      this.chartLoadingStates[key] = false;
    });

    let userAnalyticsArray = null;
    if (userConsumptionData) {
      if (userConsumptionData.userLevelAnalytics && userConsumptionData.userLevelAnalytics.length > 0) {
        userAnalyticsArray = userConsumptionData.userLevelAnalytics;
      } else if (userConsumptionData.userAnalytics && userConsumptionData.userAnalytics.length > 0) {
        userAnalyticsArray = userConsumptionData.userAnalytics;
      } else if (Array.isArray(userConsumptionData) && userConsumptionData.length > 0) {
        userAnalyticsArray = userConsumptionData;
      }
    }

    if (userAnalyticsArray && userAnalyticsArray.length > 0) {
      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(userAnalyticsArray);
      this.sortedAnalytics = userAnalyticsArray;
      this.noDataAvailable = false;
      this.usageLoader = false;
    } else {
      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');
      this.sortedAnalytics = [];
      this.noDataAvailable = true;
      this.usageLoader = false;
    }

    if (mainData.linesOfCodeAnalytics && mainData.linesOfCodeAnalytics.length > 0) {
      const categories = mainData.linesOfCodeAnalytics.map((item: any) => item.date);
      const values = mainData.linesOfCodeAnalytics.map((item: any) => item.count);
      this.chartOptions['linesOfCodeProcessed'] = this.createLineChart('Lines of code Processed', categories, values, 'Lines of Code');
    } else {
      this.chartOptions['linesOfCodeProcessed'] = this.createNoDataChart('Lines of code Processed');
    }

    if (mainData.useCaseLevelAnalytics && mainData.useCaseLevelAnalytics.length > 0) {
      const sortedUseCases = mainData.useCaseLevelAnalytics
        .sort((a: any, b: any) => b.count - a.count)
        .slice(0, 5);
      const categories = sortedUseCases.map((item: any) => item.useCaseCode);
      const values = sortedUseCases.map((item: any) => item.count);
      this.chartOptions['topUseCases'] = this.createColumnChart('Top 5 Individual Agents', categories, values, 'Usage Count', 'agents');
    } else {
      this.chartOptions['topUseCases'] = this.createNoDataChart('Top 5 Individual Agents');
    }

    let requestData = mainData.numberOfRequestsAnalytics || mainData.numberOfRequestAnalytics;
    if (requestData && requestData.length > 0) {
      const categories = requestData.map((item: any) => item.date);
      const values = requestData.map((item: any) => item.requestCount);
      this.chartOptions['numberOfRequests'] = this.createLineChart('Number of Requests', categories, values, 'Request Count');
    } else {
      this.chartOptions['numberOfRequests'] = this.createNoDataChart('Number of Requests');
    }

    if (mainData.programmingLanguageAnalytics && mainData.programmingLanguageAnalytics.length > 0) {
      const categories = mainData.programmingLanguageAnalytics.map((item: any) => item.programmingLanguage);
      const values = mainData.programmingLanguageAnalytics.map((item: any) => item.count);
      this.chartOptions['topLanguages'] = this.createColumnChart('Top 5 Languages / Frameworks', categories, values, 'Usage Count', 'languages');
    } else {
      this.chartOptions['topLanguages'] = this.createNoDataChart('Top 5 Languages / Frameworks');
    }

    if (mainData.userResponseAnalytics && mainData.userResponseAnalytics.length > 0) {
      const pieData = mainData.userResponseAnalytics.map((item: any) => [item.response, item.percentage]);
      this.chartOptions['userResponse'] = this.createPieChart('User Response', pieData, 'userResponse');
    } else {
      this.chartOptions['userResponse'] = this.createNoDataChart('User Response');
    }

    let activeUsers = 0;
    let inactiveUsers = 0;
    let totalUsers = 0;

    if (userActivityData && userActivityData.userActivity) {
      const activity = userActivityData.userActivity;
      activeUsers = activity.activeUsers || 0;
      inactiveUsers = activity.inactiveUsers || 0;
      totalUsers = activity.totalUsers || 0;
    } else if (userActivityData) {
      activeUsers = userActivityData.activeUsers || 0;
      inactiveUsers = userActivityData.inactiveUsers || 0;
      totalUsers = userActivityData.totalUsers || 0;
    }

    const total = totalUsers || (activeUsers + inactiveUsers);
    console.log('Active Users:', activeUsers, 'Inactive Users:', inactiveUsers, 'Total:', total);

    if (total > 0) {
      const inactivePercentage = (inactiveUsers / total) * 100;
      const activePercentage = (activeUsers / total) * 100;
      const pieData = [
        ['Active Users', activePercentage],
        ['Inactive Users', inactivePercentage]
      ];
      this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', pieData, 'dormantUser');
      this.userActivity = { activeUsers, dormantUsers: inactiveUsers, totalUsers: total };
    } else {
      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');
    }

    let categories: string[] = [];
    let values: number[] = [];

    if (responseTimeData) {
      if (responseTimeData.responseTimes && Array.isArray(responseTimeData.responseTimes)) {
        categories = responseTimeData.responseTimes.map((item: any) => item.createdAt);
        values = responseTimeData.responseTimes.map((item: any) => item.responseTime);
      } else if (responseTimeData.categories && responseTimeData.series) {
        categories = responseTimeData.categories;
        values = responseTimeData.series;
      } else if (responseTimeData.categories && responseTimeData.ySeries) {
        categories = responseTimeData.categories;
        values = responseTimeData.ySeries;
      } else if (Array.isArray(responseTimeData) && responseTimeData.length > 0) {
        categories = responseTimeData.map((item: any) => item.createdAt || item.date);
        values = responseTimeData.map((item: any) => item.responseTime || item.time);
      }
    }

    if (categories.length > 0 && values.length > 0) {
      this.chartOptions['responseTime'] = this.createLineChart('Response Time', categories, values, 'Response Time (ms)');
    } else {
      this.chartOptions['responseTime'] = this.createNoDataChart('Response Time');
    }
  }

  private loadAgentData(): void {
    this.isLoading = true;
    const formattedDateRange = this.getFormattedDateRangeForAPI();
    this.analyticsService.getAgenticAIAnalytics(formattedDateRange)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.processAgentAnalyticsData(data);
          this.loadAgentCharts(data);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading agent data:', error);
          this.isLoading = false;
        }
      });
  }

  private processAgentAnalyticsData(data: any): void {
    this.agentMetrics = data;
    this.userActivityStats = data.userActivityStats;
    this.toolUsage = data.toolUsage || [];
    this.userConsumption = data.userConsumption || [];
  }



  toggleRowExpansion(index: number): void {
    if (this.expandedRows.has(index)) {
      this.expandedRows.delete(index);
    } else {
      this.expandedRows.add(index);
    }
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRows.has(index);
  }

  private loadAgentMetricsTable(data: any): void {
    this.agentMetricsLoader = true;
    if (data && data.agentMetrics && data.agentMetrics.length > 0) {
      this.agentMetricsTableData = data.agentMetrics;
      this.agentMetricsNoDataAvailable = false;
    } else {
      this.agentMetricsTableData = [];
      this.agentMetricsNoDataAvailable = true;
    }
    this.agentMetricsLoader = false;
  }







  private loadAgentCharts(data: any): void {

    this.agentMetrics = data;

    if (data.studioUsage && data.studioUsage.length > 0) {
      const studioUsageData = data.studioUsage.map((item: any) => [item.domainName, item.percentage]);
      this.chartOptions['studioUsage'] = this.createPieChart('Studio Usage', studioUsageData, 'studioUsage');
    } else {
      this.chartOptions['studioUsage'] = this.createNoDataChart('Studio Usage');
    }

    if (data.topAgents && data.topAgents.length > 0) {
      const categories = data.topAgents.slice(0, 5).map((agent: any) => agent.agentName);
      const series = data.topAgents.slice(0, 5).map((agent: any) => agent.usageCount);
      this.chartOptions['topAgents'] = this.createColumnChart('Top 5 Collaborative Agents', categories, series, 'Usage Count', 'topAgents');
    } else {
      this.chartOptions['topAgents'] = this.createNoDataChart('Top 5 Collaborative Agents');
    }

    if (data.agentCreated && data.agentCreated.length > 0) {
      const categories = data.agentCreated.map((item: any) => item.teamName);
      const series = data.agentCreated.map((item: any) => item.usageCount);
      this.chartOptions['agentCreated'] = this.createColumnChart('Collaborative Agent Created', categories, series, 'Usage Count', 'agentCreated');
    } else {
      this.chartOptions['agentCreated'] = this.createNoDataChart('Collaborative Agent Created');
    }

    this.loadAgentMetricsTable(data);

    if (data.toolAnalytics && data.toolAnalytics.length > 0) {
      const toolAnalyticsItem = data.toolAnalytics[0];
      const toolAnalyticsData = [
        ['User Defined Tools', toolAnalyticsItem.userDefinedPercentage],
        ['Built-in Tools', toolAnalyticsItem.builtInPercentage]
      ];
      this.chartOptions['toolAnalytics'] = this.createPieChart('Tool Analytics', toolAnalyticsData, 'default');
    } else {
      this.chartOptions['toolAnalytics'] = this.createNoDataChart('Tool Analytics');
    }

    if (data.toolUsage && data.toolUsage.length > 0) {
      const categories = data.toolUsage.slice(0, 5).map((tool: any) => tool.toolName);
      const series = data.toolUsage.slice(0, 5).map((tool: any) => tool.usageCount);
      this.chartOptions['toolUsage'] = this.createHorizontalBarChart('Tool Usage', categories, series, 'Usage Count', 'toolUsage');
    } else {
      this.chartOptions['toolUsage'] = this.createNoDataChart('Tool Usage');
    }

    if (data.adoptionRate && data.adoptionRate.length > 0) {
      const categories = data.adoptionRate.map((item: any) => item.workflowName);
      const series = data.adoptionRate.map((item: any) => item.executionCount);
      this.chartOptions['adoptionRate'] = this.createLineChart('Adoption Rate', categories, series, 'Execution Count');
    } else {
      this.chartOptions['adoptionRate'] = this.createNoDataChart('Adoption Rate');
    }

    if (data.userConsumption && data.userConsumption.length > 0) {
      const transformedData = data.userConsumption.map((user: any) => ({
        userSignature: user.email,
        requestCount: user.consumptionCount
      }));
      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(transformedData);
    } else {
      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');
    }

    if (data.userActivityStats && Array.isArray(data.userActivityStats) && data.userActivityStats.length > 0) {
      const userStats = data.userActivityStats[0];
      const activePercentage = userStats.activeUserPercentage || 0;
      const inactivePercentage = userStats.inactiveUserPercentage || 0;

      console.log('User stats:', userStats);
      console.log('Active percentage:', activePercentage);
      console.log('Inactive percentage:', inactivePercentage);

      if (activePercentage + inactivePercentage > 0) {
        const dormantUserData = [
          ['Active Users', Math.round(activePercentage * 100) / 100],
          ['Inactive Users', Math.round(inactivePercentage * 100) / 100]
        ];

        console.log('Dormant User Chart Data:', dormantUserData);
        this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', dormantUserData, 'dormantUser');
      } else {
        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');
      }
    } else {
      console.log('No user activity stats found');
      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');
    }
  }

  // Chart creation methods
  private createLineChart(title: string, categories: string[], data: number[], seriesName: string): Highcharts.Options {
    // Get professional color based on chart title
    let chartColor = this.CHART_COLORS.primary;
    if (title.includes('Lines of code')) {
      chartColor = this.PROFESSIONAL_COLORS.linesOfCode;
    } else if (title.includes('Number of Requests')) {
      chartColor = this.PROFESSIONAL_COLORS.numberOfRequests;
    } else if (title.includes('Response Time')) {
      chartColor = this.PROFESSIONAL_COLORS.responseTime;
    } else if (title.includes('Adoption Rate')) {
      chartColor = this.PROFESSIONAL_COLORS.adoptionRate;
    }

    const commonConfig = this.getCommonChartConfig();

    return {
      ...commonConfig,
      chart: {
        ...commonConfig.chart,
        type: 'line',
        height: this.CHART_DEFAULTS.height,
        zoomType: 'x',
        panning: {
          enabled: true,
          type: 'x'
        },
        panKey: 'shift'
      } as any,
      xAxis: {
        categories: categories,
        labels: {
          rotation: -45,
          style: {
            color: '#6B7280',
            fontSize: '10px',
            fontFamily: 'Inter, sans-serif'
          }
        },
        lineColor: '#E5E7EB',
        tickColor: '#E5E7EB'
      },
      yAxis: {
        title: {
          text: '',
          style: {
            color: '#6B7280'
          }
        },
        labels: {
          style: {
            color: '#6B7280',
            fontSize: '10px',
            fontFamily: 'Inter, sans-serif'
          }
        },
        gridLineColor: '#F3F4F6'
      },
      series: [{
        name: seriesName,
        type: 'line',
        data: data,
        color: chartColor,
        lineWidth: 3,
        marker: {
          fillColor: chartColor,
          lineColor: '#FFFFFF',
          lineWidth: 2,
          radius: 5
        },
        shadow: {
          color: chartColor,
          opacity: 0.3,
          width: 3
        }
      }],
      plotOptions: {
        line: {
          dataLabels: {
            enabled: false
          },
          marker: {
            enabled: true
          }
        }
      }
    };
  }

  private createColumnChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {
    // Get professional color based on chart type
    let color = this.PROFESSIONAL_COLORS.topUseCases; // Default green

    switch (chartType) {
      case 'languages':
        color = this.PROFESSIONAL_COLORS.topLanguages; // Red for languages
        break;
      case 'topAgents':
        color = this.PROFESSIONAL_COLORS.topAgents; // Pink for top collaborative agents
        break;
      case 'agentCreated':
        color = this.PROFESSIONAL_COLORS.agentCreated; // Teal for agent creation
        break;
      case 'agents':
        color = this.PROFESSIONAL_COLORS.topUseCases; // Green for individual agents
        break;
      default:
        color = this.PROFESSIONAL_COLORS.topUseCases; // Default green
    }

    const commonConfig = this.getCommonChartConfig();

    return {
      ...commonConfig,
      chart: {
        ...commonConfig.chart,
        type: 'column',
        height: this.CHART_DEFAULTS.height,
        zoomType: 'x',
        panning: {
          enabled: true,
          type: 'x'
        },
        panKey: 'shift'
      } as any,
      xAxis: {
        categories: categories,
        labels: {
          style: {
            color: '#6B7280',
            fontSize: '10px',
            fontFamily: 'Inter, sans-serif'
          }
        },
        lineColor: '#E5E7EB',
        tickColor: '#E5E7EB'
      },
      yAxis: {
        title: {
          text: '',
          style: {
            color: '#6B7280'
          }
        },
        labels: {
          style: {
            color: '#6B7280',
            fontSize: '10px',
            fontFamily: 'Inter, sans-serif'
          }
        },
        gridLineColor: '#F3F4F6'
      },
      series: [{
        name: seriesName,
        type: 'column',
        data: data,
        color: color,
        borderWidth: 0,
        borderRadius: 6,
        shadow: {
          color: color,
          opacity: 0.2,
          width: 2
        }
      }],
      plotOptions: {
        column: {
          dataLabels: {
            enabled: true,
            style: {
              color: '#333333',      // Dark text for better visibility
              fontSize: '12px',
              fontWeight: '600',
              textOutline: '1px contrast' // Add text outline for better readability
            }
          },
          borderRadius: 4,
          pointPadding: 0.2,    // Uniform spacing between bars
          groupPadding: 0.15,   // Uniform spacing between groups
          maxPointWidth: 60     // Maximum bar width for consistency
        }
      }
    };
  }

  private createPieChart(title: string, data: any[], chartType: string = 'default'): Highcharts.Options {
    // Professional color schemes for different pie chart types
    let colors: string[] = [];

    if (chartType === 'userResponse') {
      colors = this.PROFESSIONAL_COLORS.userResponse; // Multi-color palette
    } else if (chartType === 'dormantUser') {
      colors = this.PROFESSIONAL_COLORS.userActivity; // Green/Red for active/inactive
    } else if (chartType === 'studioUsage') {
      colors = this.PROFESSIONAL_COLORS.studioUsage; // Multi-color for studio usage
    } else if (chartType === 'default') {
      colors = this.PROFESSIONAL_COLORS.toolAnalytics; // Orange/Purple for tool analytics
    } else {
      colors = [this.CHART_COLORS.primary, this.CHART_COLORS.secondary]; // Fallback
    }

    const commonConfig = this.getCommonChartConfig();

    return {
      ...commonConfig,
      chart: {
        ...commonConfig.chart,
        type: 'pie',
        height: this.CHART_DEFAULTS.height
      },
      series: [{
        name: 'Percentage',
        type: 'pie',
        data: data,
        innerSize: '60%',
        colors: colors,
        dataLabels: {
          enabled: false
        }
      }],
      legend: {
        enabled: true,
        align: 'right',
        verticalAlign: 'middle',
        layout: 'vertical',
        itemStyle: {
          color: '#374151',
          fontSize: '11px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: '500'
        },
        symbolRadius: 8,
        itemMarginBottom: 8
      },
      plotOptions: {
        pie: {
          allowPointSelect: false,
          cursor: 'pointer',
          dataLabels: {
            enabled: false
          },
          showInLegend: true,
          borderWidth: 0
        }
      }
    };
  }

  private createUserConsumptionChart(data: any[]): Highcharts.Options {
    const categories = data.map(item => item.userSignature || item.email || item.userName || 'Unknown User');
    const values = data.map(item => item.requestCount || item.usageCount || item.count || 0);

    // Professional height for better visual appearance
    const chartHeight = this.CHART_DEFAULTS.height;
    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 users

    return {
      chart: {
        type: 'bar',
        backgroundColor: this.CHART_DEFAULTS.backgroundColor,
        height: chartHeight,
        marginLeft: 200, // Reduced margin for better space utilization
        marginRight: 80, // Add right margin for value labels
        scrollablePlotArea: showScrollbar ? {
          minHeight: categories.length * 30 + 80, // Reduced spacing
          scrollPositionY: 0
        } : undefined,
        style: {
          fontFamily: this.CHART_DEFAULTS.fontFamily
        }
      } as any,
      title: {
        text: '',
        style: {
          display: 'none'
        }
      },
      xAxis: {
        categories: categories,
        labels: {
          style: {
            color: '#333333',
            fontSize: '11px',
            fontWeight: '500',
            fontFamily: 'Mulish, sans-serif'
          },
          overflow: 'allow',
          step: 1,
          useHTML: true,
          formatter: function() {
            // Show full email at the start of each bar
            const email = this.value as string;
            return `<div style="width: 180px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${email}</div>`;
          }
        },
        lineColor: 'transparent',
        tickColor: 'transparent',
        min: 0,
        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable
      },
      yAxis: {
        title: {
          text: '',
          style: {
            color: '#666666'
          }
        },
        labels: {
          style: {
            color: '#666666',
            fontSize: '10px',
            fontFamily: 'Mulish, sans-serif'
          }
        },
        gridLineColor: 'transparent',
        lineColor: 'transparent'
      },
      series: [{
        name: 'Usage',
        type: 'bar',
        data: values,
        color: '#8B7EC8',
        borderWidth: 0,
        borderRadius: 2
      }],
      legend: {
        enabled: false
      },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: true,
            inside: false,
            align: 'right',
            x: 5, // Position labels slightly to the right of bars
            style: {
              color: '#333333',
              fontSize: '12px',
              fontWeight: '600',
              fontFamily: 'Mulish, sans-serif',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y;
            }
          },
          color: '#8B7EC8',
          borderRadius: 4,
          pointPadding: 0.15, // Reduced padding for better fit
          groupPadding: 0.05, // Reduced group padding
          maxPointWidth: 25   // Reduced max width for better proportion
        }
      },
      tooltip: {
        enabled: true,
        formatter: function() {
          return '<b>' + this.x + '</b><br/>Requests: ' + this.y;
        }
      },
      navigation: {
        buttonOptions: {
          enabled: true,
          theme: {
            stroke: '#8B7EC8',
            r: 2,
            states: {
              hover: {
                fill: '#8B7EC8',
                stroke: '#8B7EC8'
              },
              select: {
                fill: '#8B7EC8',
                stroke: '#8B7EC8'
              }
            }
          } as any
        }
      },
      exporting: {
        enabled: false
      },
      credits: {
        enabled: false
      }
    };
  }

  private createNoDataChart(title: string): Highcharts.Options {
    return {
      chart: {
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        align: 'left',
        style: {
          color: 'var(--text-color)',
          fontSize: '16px',
          fontWeight: '600'
        }
      },
      series: [],
      lang: {
        noData: 'No data available for the selected period'
      },
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '15px',
          color: 'var(--text-secondary)'
        }
      },
      exporting: {
        enabled: false
      },
      credits: {
        enabled: false
      }
    };
  }

  // Reusable method to create charts with no-data fallback
  private createChartWithFallback<T>(
    data: T[] | null | undefined,
    chartCreator: (data: T[]) => Highcharts.Options,
    title: string
  ): Highcharts.Options {
    if (data && data.length > 0) {
      return chartCreator(data);
    } else {
      return this.createNoDataChart(title);
    }
  }

  // Common chart styling configuration
  private getCommonChartConfig(): Partial<Highcharts.Options> {
    return {
      chart: {
        backgroundColor: 'transparent'
      },
      title: {
        text: '',
        style: {
          display: 'none'
        }
      },
      exporting: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      legend: {
        enabled: false
      }
    };
  }

  // Chart callback functions
  chartCallback = (chart: Highcharts.Chart, chartKey: string): void => {
    this.chartRefs[chartKey] = chart;
  };

  // Export methods
  downloadExcel(analyticsName: string): void {
    const formattedDateRange = this.getFormattedDateRangeForAPI();
    this.analyticsService.downloadChartExcel(formattedDateRange, analyticsName, 'excel');
  }

  downloadAgentExcel(analyticsName: string): void {
    const formattedDateRange = this.getFormattedDateRangeForAPI();
    this.analyticsService.downloadAgenticAIExcel(formattedDateRange, analyticsName, 'excel');
  }

  downloadDump(): void {
    const formattedDateRange = this.getFormattedDateRangeForAPI();
    this.analyticsService.downloadDump(formattedDateRange);
  }

  // Filter and dropdown methods
  onUseCaseSelectionChanged(event: any): void {
    this.selectedUseCases = event.value;
    this.filteringEnabled = this.selectedUseCases.length > 0;
  }

  setPayload(): void {
    // Apply filters and reload data
    this.applyFilter();
  }

  toggleDownloadOptions(event: Event): void {
    event.stopPropagation();
    this.showDownloadOptions = !this.showDownloadOptions;
  }

  downloadChartsAsPDF(): void {
    // Implementation for PDF download
    console.log('Downloading charts as PDF...');
    this.showDownloadOptions = false;
  }

  // Digital Ascender Style Horizontal Bar Chart
  private createHorizontalBarChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {
    // Professional colors for horizontal bar charts
    let color = this.PROFESSIONAL_COLORS.toolUsage; // Default orange for tool usage

    if (chartType === 'toolUsage') {
      color = this.PROFESSIONAL_COLORS.toolUsage; // Professional orange for tool usage
    } else if (chartType === 'userConsumption') {
      color = this.PROFESSIONAL_COLORS.collaborativeUserConsumption; // Pink for collaborative user consumption
    }

    // Professional height for better visual appearance
    const chartHeight = this.CHART_DEFAULTS.height;
    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 items

    const commonConfig = this.getCommonChartConfig();

    return {
      ...commonConfig,
      chart: {
        ...commonConfig.chart,
        type: 'bar',
        height: chartHeight,
        marginLeft: 180, // Reduced margin for better space utilization
        marginRight: 80, // Add right margin for value labels
        scrollablePlotArea: showScrollbar ? {
          minHeight: categories.length * 30 + 80, // Reduced spacing
          scrollPositionY: 0
        } : undefined,
        zoomType: 'x',
        panning: {
          enabled: true,
          type: 'x'
        },
        panKey: 'shift'
      } as any,
      xAxis: {
        categories: categories,
        title: {
          text: null
        },
        labels: {
          style: {
            color: '#333333',
            fontSize: '11px',
            fontWeight: '500',
            fontFamily: 'Mulish, sans-serif'
          },
          overflow: 'allow',
          step: 1,
          useHTML: true,
          formatter: function() {
            // Show full name at the start of each bar
            const name = this.value as string;
            return `<div style="width: 160px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${name}</div>`;
          }
        },
        lineColor: 'transparent',
        tickColor: 'transparent',
        min: 0,
        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable
      },
      yAxis: {
        min: 0,
        title: {
          text: '',
          style: {
            color: '#666666'
          }
        },
        labels: {
          style: {
            color: '#666666',
            fontSize: '10px',
            fontFamily: 'Mulish, sans-serif'
          }
        },
        gridLineColor: 'transparent',
        lineColor: 'transparent'
      },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: true,
            inside: false,
            align: 'right',
            x: 5, // Position labels slightly to the right of bars
            style: {
              color: '#333333',
              fontSize: '12px',
              fontWeight: '600',
              fontFamily: 'Mulish, sans-serif',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y;
            }
          },
          color: color,
          borderRadius: 4,
          pointPadding: 0.15, // Reduced padding for better fit
          groupPadding: 0.05, // Reduced group padding
          maxPointWidth: 25,   // Reduced max width for better proportion
          borderWidth: 0
        }
      },
      series: [{
        type: 'bar',
        name: seriesName,
        data: data,
        color: color
      }] as any,
      legend: {
        enabled: false
      },
      tooltip: {
        enabled: true,
        formatter: function() {
          return '<b>' + this.x + '</b><br/>' + seriesName + ': ' + this.y;
        }
      }
    };
  }

  // Common chart configuration helper
  private getBaseChartConfig(type: 'column' | 'bar' | 'pie' | 'line', height: number = this.CHART_DEFAULTS.height): any {
    return {
      chart: {
        type,
        backgroundColor: this.CHART_DEFAULTS.backgroundColor,
        height,
        style: {
          fontFamily: this.CHART_DEFAULTS.fontFamily
        }
      },
      title: {
        text: '',
        style: { display: 'none' }
      },
      credits: { enabled: false },
      legend: { enabled: false }
    };
  }

  // Utility methods for bar charts
  getBarWidth(value: number, isToolUsage: boolean = false): number {
    if (isToolUsage) {
      const maxValue = Math.max(...this.toolUsage.map(tool => tool.usageCount));
      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;
      // Ensure minimum width of 25% to show tool names
      return Math.max(calculatedWidth, 25);
    } else {
      // Handle both individual and collaborative user consumption data
      let maxValue = 0;

      if (this.activeTab === 'usecase' && this.sortedAnalytics && this.sortedAnalytics.length > 0) {
        // Individual tab - use sortedAnalytics
        maxValue = Math.max(...this.sortedAnalytics.map(user => user.requestCount || 0));
      } else if (this.activeTab === 'agents' && this.userConsumption && this.userConsumption.length > 0) {
        // Collaborative tab - use userConsumption
        maxValue = Math.max(...this.userConsumption.map(user => user.consumptionCount || user.requestCount || 0));
      }

      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;
      // Ensure minimum width of 30% to show email addresses
      return Math.max(calculatedWidth, 30);
    }
  }



  // Navigation methods
  public goToLangfuse() {
    window.open(this.langfuseUrl, '_blank');
  }

  public goToTrulens() {
    window.open(this.ICLAnalyticsUrl, '_blank');
  }

  public goToIclAnalytics() {
    window.open('https://aava-trulens-dev.avateam.io/', '_blank');
  }

  public onRangeSelected(event: any) {
    if (event && event.startDate && event.endDate) {
      // Convert dates to the format expected by the component
      const startDate = new Date(event.startDate);
      const endDate = new Date(event.endDate);

      this.dateRange.fromDate = startDate.toISOString().split('T')[0];
      this.dateRange.toDate = endDate.toISOString().split('T')[0];

      // Update form controls
      this.vmFG.patchValue({
        fromDate: this.dateRange.fromDate,
        toDate: this.dateRange.toDate
      });

      // Don't auto-apply filter - let user click the filter button
      console.log('Date range selected:', this.dateRange);
    }
  }

  getFormattedDateRange(): string {
    if (!this.dateRange.fromDate || !this.dateRange.toDate) {
      return 'No dates selected';
    }

    const fromDate = new Date(this.dateRange.fromDate);
    const toDate = new Date(this.dateRange.toDate);

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };

    return `${fromDate.toLocaleDateString('en-US', options)} to ${toDate.toLocaleDateString('en-US', options)}`;
  }

  getCalendarDateRange(): { start: Date | null; end: Date | null } {
    if (!this.dateRange.fromDate || !this.dateRange.toDate) {
      return { start: null, end: null };
    }

    return {
      start: new Date(this.dateRange.fromDate),
      end: new Date(this.dateRange.toDate)
    };
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    const downloadButton = target.closest('.download-button');
    const downloadDropdown = target.closest('.download-dropdown');

    if (!downloadButton && !downloadDropdown) {
      this.showDownloadOptions = false;
    }
  }

  public onDownloadToggle(event: any) {
    event.stopPropagation();
    this.showDownloadOptions = !this.showDownloadOptions;
    console.log('Download toggle clicked, showDownloadOptions:', this.showDownloadOptions);
  }

  public onPdfDownload() {
    this.downloadChartsAsPDF();
    this.showDownloadOptions = false;
  }

  public onDataDownload() {
    if (this.activeTab === 'usecase') {
      this.downloadDump();
    } else {
      this.downloadAgentExcel('agentAnalytics');
    }
    this.showDownloadOptions = false;
  }

}
