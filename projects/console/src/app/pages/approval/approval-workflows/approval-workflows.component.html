<div class="approval-right-screen">
    <div class="approval-title-filter">
        <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="labels.totalApprovals" [value]="totalApprovals"
            [description]="currentTab + ' ' + labels.whichAreRequestedForApproval">
        </ava-text-card>
        <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="labels.totalApprovedApprovals" [value]="totalApprovedApprovals"
            [description]="currentTab + ' ' + labels.whichAreApproved">
        </ava-text-card>
        <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="labels.totalPendingApprovals" [value]="totalPendingApprovals"
            [description]="labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval">
        </ava-text-card>
    </div>
    
<div class="filter-section">
    <div class="search-bars">
        <!-- <ava-dropdown [dropdownTitle]="labels.allStatus" [options]="options" [search]="true"
            (selectionChange)="onSelectionChange($event)">
        </ava-dropdown>
        <ava-dropdown [dropdownTitle]="labels.allPriority" [options]="options" [search]="true"
            (selectionChange)="onSelectionChange($event)">
        </ava-dropdown> -->
        <!-- <ava-button label="Bulk Approve" (userClick)="uClick(1)" variant="primary" size="large" state="default"
            iconPosition="left"></ava-button> -->
    </div>
    <div class="textbox section">
        <div>
            <form [formGroup]="searchForm">
                <ava-textbox [placeholder]="labels.searchPlaceholder" formControlName="search">
                    <ava-icon slot="icon-start" iconName="search" [iconSize]="16" iconColor="var(--color-brand-primary)"></ava-icon>
                </ava-textbox>
            </form>
            <ava-button label="Primary" variant="primary" iconName="calendar-days" iconPosition="only"
            [customStyles]="{
                background:
                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214',
              }" (userClick)="uClick($event)"></ava-button>
        </div>
    </div>
</div>
    
    <div class="approval-card-section">
        @if(totalRecords > 0){        
            <div class="approval-card-header">
                All - {{totalRecords}} {{currentTab}}
            </div>
            @for (item of consoleApproval.contents; track $index){
            <div class="approval-card-wrapper">
                <ava-approval-card height="300">
                    <div header>
                        <ava-icon iconSize="20" iconName="ellipsis-vertical"></ava-icon>
                        <div class="header">
                            <h2>{{item.session1.title}}</h2>
                            <ava-tag label="{{currentTab}}" color="info" size="sm"></ava-tag>
                        </div>
                    </div>
                    <div content class="a-content">
                        <div class="box tag-wrapper">
                            <ava-tag label="Individual" size="sm"></ava-tag>
                            <ava-tag label="Ascendion" size="sm"></ava-tag>
                            <ava-tag label="Digital Ascender" size="sm"></ava-tag>
                            <ava-tag label="Platform Engineering" size="sm"></ava-tag>
                        </div>
            
                        <div class="box info-wrapper">
                            <div class="f">
                                <ava-icon iconSize="13" iconName="user"></ava-icon>
                                <span>{{item.session3[0].label}}</span>
                            </div>
                            <div class="ml-auto s">
                                <ava-icon iconSize="20" iconName="calendar-days"></ava-icon>
                                <span>{{item.session3[1].label}}</span>
                            </div>
                        </div>
                    </div>
                    <div footer>
                        <div class="footer-content">
                            <div class="footer-left">
                                <span class="ex">Execution Status</span>
                                <div>
                                    <ava-icon iconSize="20" iconName="circle-check-big"></ava-icon>
                                    <span>{{item?.session4.status}}</span>
                                </div>
            
                            </div>
                            <div class="footer-right">
                                <ava-button [label]="labels.test" (userClick)="handleTesting($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" iconName="play" iconPosition="left"></ava-button>
                                <ava-button [label]="labels.sendback" (userClick)="rejectApproval($index)" variant="secondary" size="small" [customStyles]="{
                                    'border': '2px solid transparent',
                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    'background-origin': 'border-box',
                                    'background-clip': 'padding-box, border-box',
                                    '--button-effect-color': '33, 90, 214'
                                  }"
                                    state="default" iconName="move-left" iconPosition="left"></ava-button>
                                <ava-button [label]="labels.approve" (userClick)="approveApproval($index)" variant="primary" size="small" [customStyles]="{
                                    background:
                                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                                    '--button-effect-color': '33, 90, 214',
                                  }"
                                    state="default" iconName="Check" iconPosition="left"></ava-button>
                            </div>
                        </div>
                    </div>
                </ava-approval-card>
            </div>
            }
        }
        @else{
            <div class="no-pending-message">
                All {{currentTab}} have been successfully approved. No pending actions.
            </div>
        }
    </div>
</div>

<ava-popup
  [show]="showToolApprovalPopup"
  [title]="labels.confirmApproval"
  [message]="labels.youAreAboutToApproveThis + ' ' + currentTab + '. ' + labels.itWillBeActiveAndAvailableIn + ' ' + currentTab + ' ' + labels.catalogueForUsersToExecute"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="labels.approve"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="handleApproval()"
  (cancel)="showToolApprovalPopup=false"
  (closed)="showToolApprovalPopup=false"
>
</ava-popup>

<ava-popup messageAlignment="center" [show]="showInfoPopup"
    title="SUCCESS!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-check" iconColor="green" [showClose]="true" (closed)="handleInfoPopup()">
</ava-popup>

<ava-confirmation-popup [show]="showFeedbackPopup" title="Confirm Send Back"
    message="This {{currentTab}} will be send back for corrections and modification. Kindly comment what needs to be done."
    confirmationLabel="Send Back" (closed)="showFeedbackPopup = false" (confirm)="handleRejection($event)">
</ava-confirmation-popup>

<ava-popup messageAlignment="center" [show]="showErrorPopup"
    title="FAILED!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-x" iconColor="red" [showClose]="true" (closed)="showErrorPopup = false" >
</ava-popup>