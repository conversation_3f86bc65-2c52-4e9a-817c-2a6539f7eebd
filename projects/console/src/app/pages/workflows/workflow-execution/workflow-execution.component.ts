import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';

// Import child components
import { ChatInterfaceComponent } from '../../../shared/components/chat-interface/chat-interface.component';
import { ChatMessage } from '../../../shared/components/chat-window/chat-window.component';
import {
  AgentActivityComponent,
  ActivityLog,
  ExecutionDetails,
} from './components/agent-activity/agent-activity.component';
import {
  AgentOutputComponent,
  AgentOutput as OutputItem,
} from './components/agent-output/agent-output.component';
import {
  ButtonComponent,
  IconComponent,
  TabItem,
  TabsComponent,
} from '@ava/play-comp-library';
import { WorkflowService } from '../../../shared/services/workflow.service';
import { environment } from 'projects/console/src/environments/environment';
import workflowConstants from './../constants/workflows.json';
import { TokenStorageService } from '@shared/index';
import { LoaderService } from '../../../shared/services/loader/loader.service';

export interface AvaTab {
  id: string; // required string id for TabItem compatibility
  label: string;
  content?: string;
  iconName?: string;
  subtitle?: string;
  disabled?: boolean;
  badge?: string | number;
  closeable?: boolean;
}

export enum ExecutionStatus {
  notStarted = 'notStarted',
  running = 'running',
  completed = 'completed',
}

@Component({
  selector: 'app-workflow-execution',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChatInterfaceComponent,
    AgentActivityComponent,
    AgentOutputComponent,
    TabsComponent,
    ButtonComponent,
    IconComponent,
  ],
  templateUrl: './workflow-execution.component.html',
  styleUrls: ['./workflow-execution.component.scss'],
})
export class WorkflowExecutionComponent implements OnInit, OnDestroy {
  navigationTabs: TabItem[] = [
    { id: 'nav-home', label: 'Agent Activity' },
    { id: 'nav-products', label: 'Agent Output' },
    { id: 'nav-services', label: 'Preview' },
  ];
  // Workflow details
  workflowId: string | null = null;
  workflowName: string = 'Workflow';

  constants = workflowConstants as Record<string, any>;

  @ViewChild(ChatInterfaceComponent, { static: false })
  chatInterfaceComp!: ChatInterfaceComponent;

  // Activity logs
  activityLogs: ActivityLog[] = [];
  activityProgress: number = 0;
  executionDetails?: ExecutionDetails;
  isRunning: boolean = false;
  status: ExecutionStatus = ExecutionStatus.notStarted;

  // Chat messages
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  inputText = '';

  // Agent outputs
  agentOutputs: OutputItem[] = [];
  public workflowForm!: FormGroup;
  public fileType : string = '.zip';

  // Execution state
  executionStartTime: Date | null = null;
  executionCompleted: boolean = false;

  public workflowLogs: any[] = [];
  enableStreamingLog = environment.enableLogStreaming || 'all';

  public isExecutionComplete: boolean = false;
  progressInterval: any;

  // Component lifecycle
  private destroy$ = new Subject<void>();
  selectedTab: string = 'Agent Activity';
  demoTabs: AvaTab[] = [
    { id: 'activity', label: 'Agent Activity' },
    { id: 'agents', label: 'Agent Output' },
    { id: 'preview', label: 'Preview', disabled: true },
  ];
  errorMsg = false;
  resMessage: any;
  taskMessage = [];
  isJsonValid = false;
  disableChat : boolean = false;
  selectedFiles: File[] = [];
  workflowAgents: any[] = [];
  userInputList: any[] = [];
  progress = 0;
  isLoading = false;
  loaderColor: string = '';

  inputFieldOrder: string[] = [];
  currentInputIndex: number = 0;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private workflowService: WorkflowService,
    private tokenStorage: TokenStorageService,
    private loaderService: LoaderService,
    private formBuilder: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.loaderService.disableLoader();
    this.selectedTab = 'Agent Activity';
    this.executionId = crypto.randomUUID();
    // Get workflow ID from route params
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.workflowId = params.get('id');
      if (this.workflowId) {
        this.loadWorkflow(this.workflowId);
      } else {
        // No workflow ID, redirect back to workflows page
        this.router.navigate(['/build/workflows']);
      }
    });
    // this.executeWorkflow()
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.loaderService.enableLoader();
  }
  onTabChange(event: TabItem) {
    this.selectedTab = event.label;
    console.log('Tab changed:', event);
  }

  executionId!: string;

  // Load workflow data
  loadWorkflow(id: string): void {
    // In a real app, this would fetch the workflow from a service
    console.log(`Loading workflow with ID: ${id}`);
    this.chatMessages = [
      {
        from: 'ai',
        text: 'I am your workflow assistant. I will help you in executing this workflow.',
      } as ChatMessage,
    ]
    this.workflowForm = this.formBuilder.group({});
    
    this.workflowService.getWorkflowById(id).subscribe({
        next: (res) => {
        this.workflowAgents = res.workflowAgents;
        this.userInputList = this.extractInputField(this.workflowAgents);
        if(this.userInputList.length === 0){
          this.disableChat = true;
        }
        this.workflowName = res.name;        
        this.initializeForm();
        this.startInputCollection();
      },
        error: (err) => {
          this.disableChat = true;
          console.log(err);
        }
    });

  }

  public extractInputField(pipeLineAgents: any) {
    const PLACEHOLDER_PATTERNS =  /(%\d+\$s)|\{\{([a-zA-Z0-9-_]+)\}\}/g;
    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};

    pipeLineAgents.forEach((agent: any) => {
      const agentName = agent?.agentDetails?.name;
      const agentDescription = agent?.agentDetails?.description;
      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];

      for (const match of matches) { 
        const placeholder = match[1] || match[2];
        const placeholderInput = match[0];
        if (!placeholderMap[placeholder]) {
          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;
        }
        placeholderMap[placeholder].agents.add(agentName);
        placeholderMap[placeholder].inputs.add(placeholderInput);
      }
    })

    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({
      name: [...agents].length > 2
        ? `${[...agents].slice(0, -1).join(", ")} and ${[...agents].at(-1)}`
        : [...agents].join(" and "),
      placeholder,
      input: [...inputs][0],
    }));
  }

  public isImageInput(input: string): boolean {
    const match = input.match(/{{(.*?)}}/);
    if (match && match[1]) {
      const variableName = match[1].trim();
      return variableName.startsWith('image') || variableName.startsWith('Image');
    }
    return false;
  }

  public initializeForm() {   
    this.userInputList.forEach((label: any) => {
      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));
    })
  }

  public isInputValid() {
    return this.workflowForm.valid && this.workflowId;
  }

  startFakeProgress() {
    this.progress = 0;
    this.progressInterval = setInterval(() => {
      if (this.progress < 90) {
        this.progress += 5; // Increase slowly
      }
    }, 200); // Adjust speed
  }

  stopFakeProgress() {
    clearInterval(this.progressInterval);
    this.progress = 100;

    setTimeout(() => {
      this.isLoading = false;
    }, 500); // Small delay to let user see 100%
  }

  // For demonstration - initialize with sample data
  initializeWithSampleData(): void {
    // Sample activity logs
    // this.activityLogs = [
    //   {
    //     timestamp: '2025-03-23 10:15:00',
    //     message: 'Agent Pipeline started',
    //     type: 'info',
    //   },
    //   {
    //     timestamp: '2025-03-23 10:15:02',
    //     message: 'Agent LLM initialized',
    //     type: 'info',
    //   },
    //   {
    //     timestamp: '2025-03-23 10:15:05',
    //     message: 'Processing user query',
    //     type: 'info',
    //   },
    //   {
    //     timestamp: '2025-03-23 10:15:07',
    //     message: 'Agent Pipeline completed successfully',
    //     type: 'success',
    //   },
    // ];

    // Set progress to 100%
    this.activityProgress = 100;

    // Set execution details
    this.executionDetails = {
      agentName: 'Lead Qualification Agent',
      executionId: 'LQ-20250323-001',
      startTime: '2025-03-23 10:15:00 UTC',
      endTime: '2025-03-23 10:15:07 UTC',
      status: 'completed',
      steps: [
        'Initial query analysis',
        'Qualification criteria matching',
        'Response generation',
      ],
    };

    // Sample agent outputs
    // this.agentOutputs = [
    //   {
    //     id: '1',
    //     title: 'Portfolio website HTML Code',
    //     content: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8"/>\n<meta name="viewport" content="width=device-width, initial-scale=1.0" />\n<title>My Portfolio</title>\n<link rel="stylesheet" href="style.css" />\n</head>\n<body>\n<header>\n<h1>Jane Doe</h1>\n</header>\n<nav>\n<a href="#about">About</a>\n<a href="#projects">Projects</a>\n<a href="#contact">Contact</a>\n</nav>\n</body>\n</html>`,
    //     agentName: 'Test Agent',
    //     timestamp: '2025-03-23 10:15:07',
    //     type: 'code',
    //   },
    //   {
    //     id: '2',
    //     title: 'Portfolio website HTML Code',
    //     content: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8"/>\n<meta name="viewport" content="width=device-width, initial-scale=1.0" />\n<title>My Portfolio</title>\n<link rel="stylesheet" href="style.css" />\n</head>\n<body>\n<header>\n<h1>Jane Doe</h1>\n</header>\n<nav>\n<a href="#about">About</a>\n<a href="#projects">Projects</a>\n<a href="#contact">Contact</a>\n</nav>\n</body>\n</html>`,
    //     agentName: 'Test Agent 2',
    //     timestamp: '2025-03-23 10:15:07',
    //     type: 'code',
    //   },
    // ];

    // Sample chat messages
    this.chatMessages = [
      {
        from: 'ai',
        text: 'I am your workflow assistant. Please click the Send button below to execute your workflow.',
      } as ChatMessage,
      // {
      //   from: 'user',
      //   text: 'Create a basic portfolio website structure for me.',
      // } as ChatMessage,
      // {
      //   from: 'ai',
      //   text: "I've generated a basic HTML structure for your portfolio website. You can see the output in the right panel.",
      // } as ChatMessage,
    ];

    // Set execution details
    this.executionStartTime = new Date('2025-03-23T10:15:00');
    this.executionCompleted = true;
  }

  // Handle new chat message from user
  handleChatMessage(message: string): void {
    console.log(message);
    if(message.trim() === ''){
      if(this.inputFieldOrder.length > 0){
        return;
      }
    }

    if(this.inputFieldOrder.length === 0 || this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){
      this.isProcessingChat = true;
      this.chatInterfaceComp.addAiResponse('Running the workflow...');
      this.executeWorkflow();
      return;
    }

    const field = this.inputFieldOrder[this.currentInputIndex];
    if (this.isImageInput(field)) {
      // Ignore text input, wait for file input
      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);
      return;
    }

    this.workflowForm.get(field)?.setValue(message);
    this.currentInputIndex++;

    if (this.currentInputIndex < this.inputFieldOrder.length) {
      this.promptForCurrentField();
    } else {
      this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');
      this.isProcessingChat = true;
      this.executeWorkflow();
    }
  }

  // Save execution logs
  saveLogs(): void {
    console.log('Saving execution logs...');
    // This would typically save to a service
  }

  // Export results
  exportResults(section: 'activity' | 'output'): void {
    console.log(`Exporting ${section} data...`);

    if (section === 'activity') {
      const data = this.activityLogs
        .map((log) => `[${log.timestamp}] ${log.message}`)
        .join('\n');
      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');
    } else {
      const data = JSON.stringify(this.agentOutputs, null, 2);
      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');
    }
  }

  // Helper method to download data as a file
  private downloadAsFile(data: string, filename: string, type: string): void {
    const blob = new Blob([data], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }

  // Handle controls for execution
  handleControlAction(action: 'play' | 'pause' | 'stop'): void {
    console.log(`Control action: ${action}`);
    // In a real app, this would control the workflow execution

    if (action === 'play') {
      this.isRunning = true;
    } else if (action === 'pause' || action === 'stop') {
      this.isRunning = false;
    }
  }

  // Navigate back to workflow listing
  navigateBack(): void {
    this.router.navigate(['/build/workflows']);
  }

  // Navigate to edit workflow
  editWorkflow(): void {
    if (this.workflowId) {
      this.router.navigate(['/build/workflows/edit', this.workflowId]);
    }
  }

  public logExecutionStatus(delay: number = 2000) {
    setTimeout(() => {
      if (!this.isExecutionComplete) {
        console.log(this.constants);
        console.log(this.constants['labels'].workflowExecProcessing);
        this.workflowLogs.push({
          content: this.constants['labels'].workflowExecProcessing,
          color: '#F9DB24',
        });
      }
    }, delay);
  }

  // public parseAnsiString(ansiString: string) {
  //   const regex = ansiRegex();
  //   const parts = ansiString.split(regex);
  //   const matches = [...ansiString.matchAll(regex)];
  //   parts.forEach((part, index) => {
  //     if (part.trim() !== '') {
  //       let colorCode = matches[index - 1][0];
  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\u001b[1m')) {
  //         colorCode = `\u001b[1m${colorCode}`;
  //       }
  //       this.workflowLogs.push({
  //         content: part,
  //         color: this.colorMap[colorCode] || 'white',
  //       });
  //     }
  //   });
  // }

  public getWorkflowLogs(executionId: string) {
    this.workflowService
      .workflowLogConnect(executionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (message) => {
          console.log('message: ', message);
          const { content, color } = message;
          if (color) {
            this.workflowLogs.push({ content, color });
          } else if (this.enableStreamingLog === 'all') {
            // this.parseAnsiString(content);
          }
        },
        error: (err) => {
          this.workflowLogs.push({
            content: this.constants['workflowLog'],
            color: 'red',
          });
          console.error('WebSocket error:', err);
        },
        complete: () => {
          this.logExecutionStatus();
          console.log('WebSocket connection closed');
        },
      });
  }

  // public parseAnsiString(ansiString: string) {
  //   const regex = ansiRegex();
  //   const parts = ansiString.split(regex);
  //   const matches = [...ansiString.matchAll(regex)];
  //   parts.forEach((part, index) => {
  //     if (part.trim() !== '') {
  //       let colorCode = matches[index-1][0];
  //       if(index - 2 >= 0 && matches[index-2]?.includes('\u001b[1m')) {
  //         colorCode = `\u001b[1m${colorCode}`;
  //       }
  //       this.workflowLogs.push({
  //         content: part, 
  //         color: this.colorMap[colorCode] || 'white', 
  //       });
  //     }
  //   });
  // }

  public validateJson(output: string): any | null {
    this.isJsonValid = false;
    try {
      const parsedOutput = JSON.parse(output);
      this.isJsonValid = true;
      return parsedOutput;
    } catch (e) {
      return null;
    }
  }

  public executeWorkflow() {
    let payload: FormData | Record<string, any> = new FormData();
    let queryString = '';


    this.status = ExecutionStatus.running;
    if (this.selectedFiles.length) {
      this.selectedFiles.forEach((file) => {
        payload.append('files', file);
      });
      payload.append('workflowId', this.workflowId);
      payload.append('userInputs', JSON.stringify(this.workflowForm.value));
      payload.append('user', this.tokenStorage.getDaUsername());
      payload.append('executionId', this.executionId);
      queryString = '/files';
      console.log('payload', payload);
    } else {
      payload = {
        pipeLineId: this.workflowId,
        userInputs: this.workflowForm.value,
        executionId: this.executionId,
        user: this.tokenStorage.getDaUsername(),
      };
    }
    this.getWorkflowLogs(this.executionId);
    this.startFakeProgress();

    this.workflowService
      .executeWorkflow(payload, queryString)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.isProcessingChat = false;
          this.isRunning = false;
          this.isExecutionComplete = true;
          this.chatInterfaceComp.addAiResponse(res?.message || "Workflow execution completed successfully!");

          if (res?.workflowResponse?.pipeline?.output) {
            this.isExecutionComplete = true;
            // console.log(this.constants['labels'].workflowExecComplete);
            this.workflowLogs.push({
              content: this.constants['labels'].workflowExecComplete,
              color: '#0F8251',
            });
            this.errorMsg = false;
            this.resMessage = res?.workflowResponse?.pipeline?.output;
            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {
              return {
                id: task?.id || '',
                title: task?.title || '',
                content: task?.content || '',
                agentName: task?.agentName || '',
                timestamp: task?.timestamp || '',
                type: task?.type || '',
                description: task?.description || '',
                expected_output: task?.expected_output || '',
                summary: task?.summary || '',
                raw: task?.raw || '',
              };
            })

            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(
              (task: {
                description: any;
                summary: any;
                raw: any;
                expected_output: any;
              }) => {
                return {
                  description: task.description,
                  summary: task.summary,
                  raw: task.raw,
                  expected_output: task.expected_output,
                };
              },
            );

            console.log('taskMessage', this.taskMessage);

            // if("file_download_url" in res?.pipeline){
            //   this.isFileWriter = true;
            //   this.fileDownloadLink = res?.pipeline?.file_download_url;

            //   if(!this.fileDownloadLink){
            //     this.fileDownloadUrlError = [];
            //     this.fileDownloadUrlError.push("Output file is not generated yet!")
            //   }
            // }
            // this.isAccordian = true
          }
          this.validateJson(this.resMessage);
          this.status = ExecutionStatus.completed;
          this.stopFakeProgress();
          this.selectedFiles = [];
        },
        error: (error) => {
          this.isExecutionComplete = true;
          this.isProcessingChat = false;
          this.errorMsg = true;
          this.resMessage = error?.error?.detail;
          this.workflowService.workflowLogDisconnect();
          this.workflowLogs.push({
            content: this.constants['labels'].workflowLogFailed,
            color: 'red',
          });
          this.chatInterfaceComp.addAiResponse(
            'Something went wrong, Workflow execution has failed.',
          );
          this.selectedFiles = [];
          console.log('error is', error.message);
        },
      });
  }

  public asyncExecutePipeline() {
    const payload: FormData = new FormData();
    if (this.selectedFiles?.length) {
      for (const element of this.selectedFiles) {
        payload.append('files', element)
      }
    }
    payload.append('pipeLineId', String(this.workflowId));
    payload.append('userInputs', JSON.stringify(this.workflowForm.value));
    payload.append('user', this.tokenStorage.getDaUsername() || '');
    payload.append('executionId', this.executionId);

    this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({
      next: (res: any) => {
        if(res) {
          // res handling
          console.log(res);
        }
      },
      error: e => {
        // error handling
        console.log(e);
      }
    })
  }

  handleAttachment() {
    console.log('handleAttachment');
  }

  onAttachmentsSelected(files: File[]) {
    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){
      this.selectedFiles = files;
      return;
    }

    const field = this.inputFieldOrder[this.currentInputIndex];
    if(this.isImageInput(field)){
      if (files && files.length > 0) {
        this.onImageSelected(files[0]);
      }
    } else {
      this.selectedFiles = files;
    }
  }

  startInputCollection(){
    this.inputFieldOrder = Object.keys(this.workflowForm.controls);
    this.currentInputIndex = 0;
    if (this.inputFieldOrder.length > 0) {
      this.promptForCurrentField();
    }
    else{
      this.disableChat = true;
    }
  }

  promptForCurrentField() {
    const field = this.inputFieldOrder[this.currentInputIndex];
    if (this.isImageInput(field)) {
      this.fileType = '.jpeg,.png,.jpg,.svg';
      this.chatMessages.push({
        from: 'ai',
        text: `Please upload an image for ${field}`,
      } as ChatMessage);
      // UI should now show a file input for the user
    } else {
      this.fileType = '.zip'; // or whatever default you want for non-image
      this.chatMessages.push({
        from: 'ai',
        text: `Please enter the value of ${field}`,
      } as ChatMessage);
    }
  }

  onImageSelected(file: File) {
    const field = this.inputFieldOrder[this.currentInputIndex];
    if (!this.isImageInput(field)) return;

    const reader = new FileReader();
    reader.onload = () => {
      const base64String = (reader.result as string); 
      this.workflowForm.get(field)?.setValue(base64String);
      this.currentInputIndex++;
      if (this.currentInputIndex < this.inputFieldOrder.length) {
        this.promptForCurrentField();
      } else {
        this.chatMessages.push({
          from: 'ai',
          text: 'Thank you! Running the workflow...',
        } as ChatMessage);
        this.executeWorkflow();
      }
    };
    reader.readAsDataURL(file);
  }
}
