export enum APIKeys {
  totalAgentsCreated = 'totalAgentsCreated',
  totalAgentsReused = 'totalAgentsReused',
  totalTools = 'totalTools',
  toolUsage = 'toolUsage',
  agentMetrics = 'agentMetrics',
  toolName = 'toolName',
  agentName = 'agentName',
  usageCount = 'usageCount',
  workflowCount ='workflowCount'
}

export enum ActiveMonitorings {
  tools = 'Tools',
  agents = 'Agents',
}

export const ACTIVE_MONITORING_OPTIONS = Object.values(ActiveMonitorings).map(
  (value) => {
    return {
      name: value,
      value: value,
    };
  },
);

export const DASHBOARD_CARD_DETAILS = [
  {
    icon: '',
    title: 'Agents Created',
    field: APIKeys.totalAgentsCreated,
    subtitle: 'Total collaborative agents which are created',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Agents Reused',
    field: APIKeys.totalAgentsReused,
    subtitle: 'Total collaborative agents which are Reused',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Total Tools',
    field: APIKeys.totalTools,
    subtitle: 'Total Avaliable Tools',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Total agents approval',
    field: 0.0,
    subtitle: 'Total agents which are sent for approval',
    badge: '',
    value:0
  },
];
