import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Host<PERSON><PERSON><PERSON>,
  ElementRef,
  ChangeDetectorRef,
  ViewChild,
  Renderer2,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { NavItemComponent } from '../nav-item/nav-item.component';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ThemeService } from '../../services/theme/theme.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthService } from '@shared/auth/services/auth.service';
import { DropdownComponent, DropdownOption } from '@ava/play-comp-library';
import { OrgConfigService } from '../../../pages/org-config/services/org-config.service';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormGroup,
  Validators,
  FormControl,
} from '@angular/forms';
import { ButtonComponent } from '@ava/play-comp-library';

// Simple interfaces for the API response
interface Team {
  teamId: number;
  teamName: string;
}

interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

interface DropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

interface NavItem {
  label: string;
  route: string;
  selected: boolean;
  hasDropdown: boolean;
  dropdownOpen?: boolean;
  icon: string;
  dropdownItems?: DropdownItem[];
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [
    HeaderComponent,
    CommonModule,
    NavItemComponent,
    DropdownComponent,
    ButtonComponent,
  ],
  providers: [OrgConfigService],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
})
export class NavHeaderComponent implements OnInit, OnDestroy, AfterViewInit {
  logoSrc: string = '';
  themeMenuIcon: string = '';
  userAvatar: string = '';
  currentTheme: 'light' | 'dark' = 'light';

  // Profile dropdown properties
  profileDropdownOpen: boolean = false;
  userName: string = '';
  userEmail: string = '';
  public redirectUrl = '';

  // Subscription management
  private subscriptions = new Subscription();

  // Navigation menu items
  navItems: NavItem[] = [
    {
      label: 'Dashboard',
      route: '/dashboard',
      selected: true,
      hasDropdown: false,
      icon: `svgs/icons/awe_dashboard.svg`,
    },
    {
      label: 'Build',
      route: '/build',
      selected: false,
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_launch.svg`,
      dropdownItems: [
        {
          label: 'Agents',
          description: 'Create, Manage and Edit Agents',
          route: '/build/agents',
          icon: `svgs/icons/awe_agents.svg`,
        },
        {
          label: 'Workflows',
          description: 'Create, Manage and Edit Workflows',
          route: '/build/workflows',
          icon: `svgs/icons/awe_workflows.svg`,
        },
      ],
    },
    {
      label: 'Libraries',
      route: '/libraries',
      selected: false,
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_libraries.svg`,
      dropdownItems: [
        {
          label: 'Prompts',
          description: 'Create, Manage and Edit Prompts',
          route: '/libraries/prompts',
          icon: `svgs/icons/awe_prompts.svg`,
        },
        {
          label: 'Models',
          description: 'Add, Manage and View Models',
          route: '/libraries/models',
          icon: `svgs/icons/awe_models.svg`,
        },
        {
          label: 'Knowledge-base',
          description: 'Add, Manage and Edit Knowledge-Base',
          route: '/libraries/knowledge-base',
          icon: `svgs/icons/awe_knowledgebase.svg`,
        },
        {
          label: 'Tools',
          description: 'Add, Manage and Edit Tools',
          route: '/libraries/tools',
          icon: `svgs/icons/awe_tools.svg`,
        },
        {
          label: 'Guardrails',
          description: 'Add, Manage and Edit Guardrails',
          route: '/libraries/guardrails',
          icon: `svgs/icons/awe_guardrails.svg`,
        },
      ],
    },
    {
      label: 'Approvals',
      route: '/approval',
      selected: true,
      hasDropdown: false,
      dropdownOpen: false,
      icon: `svgs/icons/awe_admin_ management.svg`,
      dropdownItems: [
        {
          label: 'Approval Agents',
          description: 'Manage approval agents',
          route: '/approval/approval-agents',
          icon: `svgs/icons/awe_agents.svg`,
        },
        {
          label: 'Approval Workflows',
          description: 'Manage approval workflows',
          route: '/approval/approval-workflows',
          icon: `svgs/icons/awe_workflows.svg`,
        },
        {
          label: 'Approval Tools',
          description: 'Manage approval tools',
          route: '/approval/approval-tools',
          icon: `svgs/icons/awe_tools.svg`,
        }
      ],
    },
    {
      label: 'User Management',
      route: '/manage',
      selected: false,
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_manage.svg`,
      dropdownItems: [
        {
          label: 'Management 1',
          description: 'Management description',
          route: '/manage1',
          icon: `svgs/icons/awe_admin_ management.svg`,
        },
        {
          label: 'Users & Admins Management',
          description:
            'Add, Manage Users and Admins, Modify tokens and assign filters',
          route: '/manage/admin-management',
          icon: `svgs/icons/awe_admin_ management.svg`,
        },
      ],
    },
    {
      label: 'Analytics',
      route: '/analytics',
      selected: false,
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_analytics.svg`,
      dropdownItems: [
        {
          label: 'Agents',
          description: '',
          route: '/analytics',
          icon: `svgs/icons/awe_analytics.svg`
        },
      ],
    },
  ];

  // Org path dropdown state - using DropdownOption interface
  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];

  // Selected values (IDs for form)
  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  // Selected names (for dropdown pre-selection)
  selectedOrgName: string = '';
  selectedDomainName: string = '';
  selectedProjectName: string = '';
  selectedTeamName: string = '';

  // Track if dropdowns are open
  isOrgOpen = false;
  isDomainOpen = false;
  isProjectOpen = false;
  isTeamOpen = false;

  isOrgDialogOpen = false;

  headerConfigForm!: FormGroup;

  // Store the hierarchy data from API
  private hierarchyData: Organization[] = [];

  @ViewChild('orgPathTrigger', { static: false }) orgPathTrigger!: ElementRef;
  @ViewChild('popover', { static: false }) popoverRef!: ElementRef;

  popoverAlign: 'left' | 'right' = 'left';

  constructor(
    private elementRef: ElementRef,
    private router: Router,
    private themeService: ThemeService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private orgConfigService: OrgConfigService,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private renderer: Renderer2,
  ) {
    // Initialize headerConfigForm before any logic that uses it
    this.headerConfigForm = this.formBuilder.group({
      org: ['', Validators.required],
      domain: ['', Validators.required],
      project: ['', Validators.required],
      team: ['', Validators.required],
    });
    // Subscribe to router events to update the active menu item
    this.subscriptions.add(
      this.router.events
        .pipe(filter((event) => event instanceof NavigationEnd))
        .subscribe((event: any) => {
          this.updateActiveMenuItemByRoute(event.url);
        }),
    );
  }

  ngOnInit(): void {
    // Get redirect URL from auth service configuration
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;

    // Get the initial theme
    this.currentTheme = this.themeService.getCurrentTheme();

    // Update assets based on theme
    this.updateThemeAssets();

    // Subscribe to theme changes
    this.subscriptions.add(
      this.themeService.themeObservable.subscribe((theme) => {
        this.currentTheme = theme;
        this.updateThemeAssets();
      }),
    );

    // Load user data from cookies initially
    this.loadUserData();

    // Subscribe to authentication state changes to reload user data when auth completes
    this.subscriptions.add(
      this.authService.authState$.subscribe((isAuthenticated) => {
        if (isAuthenticated) {
          // Reload user data when authentication is successful
          this.loadUserData();
        }
      }),
    );

    // Initialize the active menu item based on the current route
    this.updateActiveMenuItemByRoute(this.router.url);
  }

  ngAfterViewInit(): void { }

  ngOnDestroy(): void {
    // Clean up all subscriptions to prevent memory leaks
    this.subscriptions.unsubscribe();
  }

  // Update assets based on the current theme
  private updateThemeAssets(): void {
    // Update theme-specific assets
    //this.logoSrc = `assets/images/Console_Logo.png`;
    this.logoSrc = `svgs/ascendion-logo/ascendion-logo-${this.currentTheme}.svg`;
    this.themeMenuIcon = `svgs/header/menu-${this.currentTheme}.svg`;
    this.userAvatar = `svgs/header/user-avatar.svg`;
  }

  // Update the active menu item based on the current route
  updateActiveMenuItemByRoute(url: string): void {
    // Reset all selections
    this.navItems.forEach((item) => {
      item.selected = false;
    });

    // Find the matching parent route or parent of a child route
    const parentItem = this.navItems.find((item) => {
      // Check if this is a direct match for the parent route
      if (url === item.route) {
        return true;
      }

      // Check if this is a dropdown parent with a matching child
      if (item.hasDropdown && item.dropdownItems) {
        // Check if the URL starts with the parent route path (for nested routes)
        // OR if any child route exactly matches the URL
        return (
          url.startsWith(item.route + '/') ||
          item.dropdownItems.some((child) => url === child.route)
        );
      }
      // NEW: Even if hasDropdown is false, check for dropdownItems (for Approvals)
      if (!item.hasDropdown && item.dropdownItems) {
        return item.dropdownItems.some((child) => url === child.route);
      }

      return false;
    });

    if (parentItem) {
      parentItem.selected = true;
    } else {
      // Default to Dashboard if no match found
      const dashboardItem = this.navItems.find(
        (item) => item.route === '/dashboard',
      );
      if (dashboardItem) {
        dashboardItem.selected = true;
      }
    }
  }

  // Listen for clicks on the document to close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if click target is inside a dropdown menu or profile dropdown
    const target = event.target as HTMLElement;
    const clickedInsideDropdown =
      !!target.closest('.dropdown-menu') ||
      !!target.closest('.dropdown-item') ||
      !!target.closest('.profile-dropdown');

    // If clicked inside dropdown, don't close menus
    if (clickedInsideDropdown) {
      return;
    }

    // Check if the click was outside the nav header
    const clickedOutsideNavHeader = !this.elementRef.nativeElement.contains(
      event.target,
    );
    if (clickedOutsideNavHeader) {
      this.closeAllDropdowns();
      this.closeProfileDropdown();
    }
  }

  // Toggle dropdown menu
  toggleDropdown(index: number): void {
    // Close profile dropdown when nav dropdown opens
    this.closeProfileDropdown();

    // Close all other dropdowns
    this.navItems.forEach((navItem, i) => {
      if (i !== index && navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });

    // Toggle the current dropdown
    this.navItems[index].dropdownOpen = !this.navItems[index].dropdownOpen;
  }

  // Close all dropdowns
  closeAllDropdowns(): void {
    this.navItems.forEach((navItem) => {
      if (navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });
  }

  // Select a menu item
  selectMenuItem(index: number): void {
    this.navItems.forEach((navItem, i) => {
      navItem.selected = i === index;
    });
  }

  // Navigate to route
  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  // Handle dropdown item selection
  onDropdownItemSelected(
    event: { route: string; label: string },
    parentIndex: number,
  ): void {
    this.selectMenuItem(parentIndex);
    this.navigateTo(event.route);

    // Close all dropdowns after selection
    this.closeAllDropdowns();
  }

  // Load user data from cookies
  loadUserData(): void {
    this.userName = this.tokenStorage.getDaName() || 'User';
    this.userEmail = this.tokenStorage.getDaUsername() || '';
  }

  // Toggle profile dropdown
  toggleProfileDropdown(): void {
    this.profileDropdownOpen = !this.profileDropdownOpen;
    // Close nav dropdowns when profile dropdown opens
    if (this.profileDropdownOpen) {
      this.closeAllDropdowns();
    }
  }

  // Close profile dropdown
  closeProfileDropdown(): void {
    this.profileDropdownOpen = false;
  }

  // Handle logout
  logout() {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        },
      });
    }
  }

  initOrgPathFromCookie() {
    const path = this.tokenStorage.getCookie('org_path');
    if (path) {
      const parts = path.split('::');
      const usecasePath = parts[0] || '';
      const usecaseIdPath = parts[1] || '';

      // Parse the IDs
      const ids = usecaseIdPath.split('@').map(Number);

      // Set form values (IDs)
      this.headerConfigForm.patchValue({
        org: ids[0]?.toString() || '',
        domain: ids[1]?.toString() || '',
        project: ids[2]?.toString() || '',
        team: ids[3]?.toString() || '',
      });

      // Store the IDs for form and the names for dropdown pre-selection
      this.selectedOrg = ids[0]?.toString() || '';
      this.selectedDomain = ids[1]?.toString() || '';
      this.selectedProject = ids[2]?.toString() || '';
      this.selectedTeam = ids[3]?.toString() || '';

      // Store the names for dropdown pre-selection
      const pathParts = usecasePath.split('@');
      this.selectedOrgName = pathParts[0] || '';
      this.selectedDomainName = pathParts[1] || '';
      this.selectedProjectName = pathParts[2] || '';
      this.selectedTeamName = pathParts[3] || '';

      // Load dropdown options
      this.loadData();
    } else {
      this.loadData();
    }
  }

  loadData(): void {
    this.orgConfigService.getOrganizationHierarchy().subscribe({
      next: (data: Organization[]) => {
        this.hierarchyData = data;
        this.loadOrganizations();

        // After loading organizations, load cascading dropdowns if we have pre-selected values
        if (this.selectedOrg) {
          this.loadDomains(this.selectedOrg);
          if (this.selectedDomain) {
            this.loadProjects(this.selectedDomain);
            if (this.selectedProject) {
              this.loadTeams(this.selectedProject);
            }
          }
        }
      },
      error: (error) => {
        console.error('Error loading data:', error);
      },
    });
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData.map((org) => ({
      name: org.organizationName,
      value: org.orgId.toString(),
    }));
  }

  onOrgSelect(event: any): void {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    const selectedOrgName = event.selectedOptions?.[0]?.name;
    if (selectedOrgId) {
      this.selectedOrg = selectedOrgId;
      this.selectedOrgName = selectedOrgName;
      this.headerConfigForm.patchValue({ org: selectedOrgId });
      this.loadDomains(selectedOrgId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ domain: '', project: '', team: '' });
      this.selectedDomain = '';
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedDomainName = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.projectOptions = [];
      this.teamOptions = [];
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map((domain) => ({
        name: domain.domainName,
        value: domain.domainId.toString(),
      }));
    } else {
      this.domainOptions = [];
    }
  }

  onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    const selectedDomainName = event.selectedOptions?.[0]?.name;
    if (selectedDomainId) {
      this.selectedDomain = selectedDomainId;
      this.selectedDomainName = selectedDomainName;
      this.headerConfigForm.patchValue({ domain: selectedDomainId });
      this.loadProjects(selectedDomainId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ project: '', team: '' });
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.teamOptions = [];
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d) => d.domainId.toString() === domainId),
    );
    if (org) {
      const domain = org.domains.find(
        (d) => d.domainId.toString() === domainId,
      );
      if (domain) {
        this.projectOptions = domain.projects.map((project) => ({
          name: project.projectName,
          value: project.projectId.toString(),
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    const selectedProjectName = event.selectedOptions?.[0]?.name;
    if (selectedProjectId) {
      this.selectedProject = selectedProjectId;
      this.selectedProjectName = selectedProjectName;
      this.headerConfigForm.patchValue({ project: selectedProjectId });
      this.loadTeams(selectedProjectId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ team: '' });
      this.selectedTeam = '';
      this.selectedTeamName = '';
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d) =>
        d.projects.some((p) => p.projectId.toString() === projectId),
      ),
    );
    if (org) {
      const domain = org.domains.find((d) =>
        d.projects.some((p) => p.projectId.toString() === projectId),
      );
      if (domain) {
        const project = domain.projects.find(
          (p) => p.projectId.toString() === projectId,
        );
        if (project) {
          this.teamOptions = project.teams.map((team) => ({
            name: team.teamName,
            value: team.teamId.toString(),
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    const selectedTeamName = event.selectedOptions?.[0]?.name;
    if (selectedTeamId) {
      this.selectedTeam = selectedTeamId;
      this.selectedTeamName = selectedTeamName;
      this.headerConfigForm.patchValue({ team: selectedTeamId });
    }
  }

  toggleOrgDialog() {
    this.isOrgDialogOpen = !this.isOrgDialogOpen;
    if (this.isOrgDialogOpen) {
      this.initOrgPathFromCookie();
      setTimeout(() => this.adjustPopoverAlignment(), 0);
    }
  }

  adjustPopoverAlignment() {
    const trigger = this.orgPathTrigger?.nativeElement;
    const popover = this.popoverRef?.nativeElement;
    if (trigger && popover) {
      const triggerRect = trigger.getBoundingClientRect();
      const popoverRect = popover.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Check horizontal overflow
      const wouldOverflowRight =
        triggerRect.left + popoverRect.width > viewportWidth;
      const wouldOverflowLeft = triggerRect.left < 0;

      // Check vertical overflow
      const wouldOverflowBottom =
        triggerRect.bottom + popoverRect.height > viewportHeight;

      if (wouldOverflowRight) {
        this.popoverAlign = 'right';
      } else if (wouldOverflowLeft) {
        this.popoverAlign = 'left';
      } else {
        this.popoverAlign = 'left';
      }

      // Force change detection to apply the new alignment
      this.cdr.detectChanges();
    }
  }

  get orgLabel(): string {
    // Try to get the org name from the org_path cookie
    const orgPath = this.tokenStorage.getCookie('org_path');
    if (orgPath) {
      const orgName = orgPath.split('::')[0].split('@')[0];
      if (orgName) return orgName;
    }
    // Fallback to dropdown label
    return (
      this.orgOptions.find((o) => o.value === this.selectedOrg)?.name ||
      'Select Organization'
    );
  }

  get orgControl() {
    return this.headerConfigForm.get('org') as FormControl;
  }
  get domainControl() {
    return this.headerConfigForm.get('domain') as FormControl;
  }
  get projectControl() {
    return this.headerConfigForm.get('project') as FormControl;
  }
  get teamControl() {
    return this.headerConfigForm.get('team') as FormControl;
  }

  saveOrgPathAndClose() {
    // Get current form values (IDs)
    const currentFormValues = this.headerConfigForm.value;
    const newIdsPath = Object.values(currentFormValues).join('@');

    // Get current labels from the options arrays using the selected IDs
    const orgName =
      this.orgOptions.find((o) => o.value === this.selectedOrg)?.name || '';
    const domainName =
      this.domainOptions.find((d) => d.value === this.selectedDomain)?.name ||
      '';
    const projectName =
      this.projectOptions.find((p) => p.value === this.selectedProject)?.name ||
      '';
    const teamName =
      this.teamOptions.find((t) => t.value === this.selectedTeam)?.name || '';

    const currentLabels = [orgName, domainName, projectName, teamName];
    const newLabelsPath = currentLabels
      .filter((label) => label.trim() !== '')
      .join('@');
    const newPath = `${newLabelsPath}::${newIdsPath}`;

    if (newPath) {
      // Delete existing cookie and set new one for clean update
      this.tokenStorage.deleteCookie('org_path');
      this.tokenStorage.setCookie('org_path', newPath);
      this.closeOrgDialog();
      this.router.navigate(['/dashboard']);
    }
  }

  closeOrgDialog() {
    this.isOrgDialogOpen = false;
  }
}
