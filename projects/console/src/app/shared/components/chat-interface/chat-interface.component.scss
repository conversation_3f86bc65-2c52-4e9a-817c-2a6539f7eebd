@import url("https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;500;600;700&display=swap");

.chat-interface-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: var(--agent-card-bg, #ffffff);
  border-radius: 8px;
  overflow: hidden;
  font-family: "Mulish", sans-serif;
}

.messages-scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  max-height: calc(100% - 150px);
}

.messages-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;
}

.message-wrapper {
  display: flex;
  width: 100%;

  &.user {
    justify-content: flex-end;

    .message-bubble {
      background-color: var(--chat-user-message-bg, #f8d7e3);
      color: var(--chat-user-message-text, #333333);
      border-radius: 8px;
      max-width: 100%;
    }
  }

  &.ai {
    justify-content: flex-start;

    .message-bubble {
      background-color: var(--chat-ai-message-bg, #f0f2f5);
      color: var(--chat-ai-message-text, #333333);
      border-radius: 18px 18px 18px 4px;
      max-width: 100%;
    }
  }
}

.message-bubble {
  padding: 12px 16px;
  max-width: 70%;
  word-break: break-word;
  font-size: 15px;
  line-height: 1.5;
  position: relative;
  box-shadow: 0 1px 2px var(--chat-message-shadow, rgba(0, 0, 0, 0.05));
}

.message-spacer {
  height: 12px;
  width: 100%;
}

.prompt-container {
  flex-shrink: 0;
}

.prompt-input-wrapper {
  display: flex;
  border-radius: 16px;
  flex-direction: column;
  position: relative;
  min-height: 100px;
  max-height: 150px;
  border-radius: 12px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 6px var(--chat-input-shadow, rgba(0, 0, 0, 0.03));
}

.prompt-input {
  flex: 1;
  width: 100%;
  padding: 16px 20px 56px 20px;
  border: none;
  outline: none;
  font-size: 15px;
  color: var(--input-text, #333333);
  background-color: var(--chat-input-bg, #f9fafb);
  font-family: "Mulish", sans-serif;
  resize: none;
  border: 2px solid; /* Required for border-image to work */
  border-image-source: linear-gradient(91.99deg, #03acc1 1.36%, #1e65e1 100%);
  border-image-slice: 1; /* Important: makes the gradient stretch across the border */

  &:disabled {
    background-color: var(--chat-input-disabled-bg, #f7f7f7);
    cursor: not-allowed;
  }

  &::placeholder {
    color: var(--input-placeholder, #9ca3af);
    font-family: "Mulish", sans-serif;
  }
}

.prompt-actions {
  position: absolute;
  right: 20px;
  bottom: 18px;
  display: flex;
  gap: 20px;
  align-items: center;
}

.action-icon {
  background: none;
  border: none;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:focus {
    outline: none;
  }

  svg {
    width: 20px;
    height: 20px;

    path {
      stroke: var(--button-start-gradient, #6566cd);
    }
  }
}

.dot-typing {
  display: flex;
  align-items: center;
  gap: 4px;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--chat-typing-dot, #9ca3af);
    animation: dot-pulse 1.5s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.3s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
}

@keyframes dot-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.files-container {
  width: 100%;
  height: 200px;
  padding: 1rem 1rem 0 1rem;
  display: flex;
  gap: 12px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

.file-bubble {
  border: 1px solid #c5c5c5;
  padding: 10px 12px;
  border-radius: 8px;
  max-width: 450px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.file-icon {
  width: 30px;
  height: 30px;
  background: #90a4ae;
  color: #fff;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}

.file-details {
  flex-grow: 1;
  overflow: hidden;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  padding-right: 0.5rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.delete-btn {
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ef4444;
  border: none;
  border-radius: 50%;
  padding: 0;
  color: white;
  cursor: pointer;
  z-index: 999;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: #dc2626;
}

::ng-deep .prompt-actions svg path {
  stroke: #143681 !important;
}
