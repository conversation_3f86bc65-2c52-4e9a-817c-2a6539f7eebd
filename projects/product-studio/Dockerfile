# =============================================================================
# Multi-stage Dockerfile for Product Studio Angular Application
# =============================================================================
# 
# This Dockerfile uses a multi-stage build approach:
# 1. Builder stage: Compiles the Angular application
# 2. Production stage: Serves the built application with Nginx
# =============================================================================

# =============================================================================
# STAGE 1: BUILDER
# =============================================================================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# =============================================================================
# Build Arguments
# =============================================================================
ARG NODE_ENV=production
ARG BASE_URL=localhost
ARG API_BASE_URL=http://localhost:3000

# =============================================================================
# Install Dependencies
# =============================================================================
# Copy package files first for better layer caching
COPY package*.json ./
COPY angular.json ./
COPY tsconfig.json ./
COPY .npmrc ./
COPY ava-play-comp-library-1.0.80.tgz ./

# Install all dependencies (including dev dependencies for build)
# Ensure NODE_ENV is not set to production during install to include dev dependencies
ENV NODE_ENV=development
RUN npm ci

# Explicitly install build dependencies that might be missing
RUN npm install @angular-devkit/build-angular @angular/cli

# Reset NODE_ENV for build
ENV NODE_ENV=production

# =============================================================================
# Copy Source Code
# =============================================================================
# Copy application source and shared libraries
COPY projects/product-studio ./projects/product-studio
COPY projects/shared ./projects/shared

# =============================================================================
# Build Application
# =============================================================================
# Use the npm script which should work with local dependencies
RUN npm run build:product-studio

# =============================================================================
# STAGE 2: PRODUCTION
# =============================================================================
FROM nginx:alpine AS production

# =============================================================================
# Install Dependencies
# =============================================================================
# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# =============================================================================
# Create Non-Root User
# =============================================================================
# Create user and group for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S angular -u 1001

# =============================================================================
# Configure Nginx
# =============================================================================
# Copy custom nginx configuration
COPY projects/product-studio/nginx.conf /etc/nginx/nginx.conf

# =============================================================================
# Copy Built Application
# =============================================================================
# Copy built application from builder stage
COPY --from=builder --chown=angular:nodejs /app/dist/product-studio /usr/share/nginx/html

# Copy environment configuration
COPY config/env-config.js /usr/share/nginx/html/env-config.js

# =============================================================================
# Set Permissions
# =============================================================================
# Create necessary directories and set proper ownership
RUN mkdir -p /var/cache/nginx /var/log/nginx /tmp/nginx /tmp && \
    chown -R angular:nodejs /var/cache/nginx /var/log/nginx /tmp/nginx /tmp /etc/nginx/conf.d

# =============================================================================
# Switch to Non-Root User
# =============================================================================
USER angular

# =============================================================================
# Expose Port
# =============================================================================
EXPOSE 8080

# =============================================================================
# Health Check
# =============================================================================
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# =============================================================================
# Start Application
# =============================================================================
# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start nginx in foreground
CMD ["nginx", "-g", "daemon off;"] 