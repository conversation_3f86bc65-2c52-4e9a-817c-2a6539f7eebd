import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';
import { PromptSubmissionGuard } from './shared/guards/prompt-submission.guard';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },

  // Main landing page route
  {
    path: '',
    canActivate: [AuthGuard],
    loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
    data: { preload: true }
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'prompt',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
        m => m.PromptContentComponent
      ),
  },
  // Legacy route - keep for backward compatibility
  {
    path: 'code-preview',
    canActivate: [AuthGuard, PromptSubmissionGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
  },
  // Generate Application route - handles both prompt and code-preview states
  {
    path: 'generate-application',
    canActivate: [AuthGuard, CardSelectionGuard],
    loadComponent: () =>
      import('./shared/components/application-flow/application-flow.component').then(
        m => m.ApplicationFlowComponent
      ),
    data: { cardType: 'Generate Application' }
  },
  // Generate Application project loading route
  {
    path: 'generate-application/projects/:projectId',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate Application',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // New route for project loading
  {
    path: 'project/:projectId',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Project Loading',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // Generate UI Design route - handles both prompt and code-preview states
  {
    path: 'generate-ui-design',
    canActivate: [AuthGuard, CardSelectionGuard],
    loadComponent: () =>
      import('./shared/components/ui-design-flow/ui-design-flow.component').then(
        m => m.UIDesignFlowComponent
      ),
    data: { cardType: 'Generate UI Design' }
  },
  // Generate UI Design project loading route
  {
    path: 'generate-ui-design/projects/:projectId',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      cardType: 'Generate UI Design',
      isProjectLoading: true,
      skipGuards: true
    }
  },
  // Catch-all route - redirect to login
  {
    path: '**',
    redirectTo: '/login',
  },
];
